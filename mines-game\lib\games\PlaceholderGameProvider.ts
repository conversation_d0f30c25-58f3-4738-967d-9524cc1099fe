import { BaseGameProvider } from './BaseGameProvider';
import { GameConfig, GameType, GameState, GameAction } from '@/types';

/**
 * Placeholder Game Provider - For games that are not yet implemented
 * This allows us to show games in the lobby while they're under development
 */
export class PlaceholderGameProvider extends BaseGameProvider<GameState> {
  public readonly gameType: GameType;
  public readonly config: GameConfig;

  constructor(config: GameConfig) {
    super();
    this.gameType = config.id;
    this.config = config;
  }

  /**
   * Validate game parameters (placeholder implementation)
   */
  public validateGameParams(params: any): boolean {
    // Basic validation for placeholder games
    return params && typeof params.betAmount === 'number' && params.betAmount > 0;
  }

  /**
   * Calculate multiplier (placeholder implementation)
   */
  public calculateMultiplier(gameState: GameState, params?: any): number {
    return gameState.current_multiplier || 1.0;
  }

  /**
   * Generate game data (placeholder implementation)
   */
  public generateGameData(params: any): Partial<GameState> {
    const baseData = this.generateBaseGameData(params.userId, params.betAmount, params.clientSeed);

    return {
      ...baseData,
      game_type: this.gameType
    };
  }

  /**
   * Process game action (placeholder implementation)
   */
  public async processGameAction(gameState: GameState, action: GameAction): Promise<GameState> {
    // Placeholder games don't actually process actions
    // They just return the current state
    return gameState;
  }
}

// Create placeholder game configurations
export const placeholderGames: GameConfig[] = [
  // Crash game removed - using real CrashGameProvider instead
  {
    id: 'plinko',
    name: 'Plinko',
    description: 'Drop balls down the plinko board and watch them bounce into different multiplier slots. Pure luck and excitement!',
    icon: '🏀',
    category: 'originals',
    minBet: 0.01,
    maxBet: 1000,
    houseEdge: 0.01,
    maxMultiplier: 1000,
    features: ['Provably Fair', 'Multiple Risk Levels', 'Animated Gameplay'],
    isActive: true,
    isFeatured: true,
    isNew: false
  },
  {
    id: 'wheel',
    name: 'Wheel',
    description: 'Spin the wheel of fortune and win big! Choose your risk level and watch the wheel decide your fate.',
    icon: '🎡',
    category: 'originals',
    minBet: 0.01,
    maxBet: 1000,
    houseEdge: 0.01,
    maxMultiplier: 50,
    features: ['Provably Fair', 'Multiple Risk Levels', 'Visual Spinning'],
    isActive: true,
    isFeatured: false,
    isNew: true
  },
  {
    id: 'blackjack',
    name: 'Blackjack',
    description: 'Classic card game where you try to get as close to 21 as possible without going over. Beat the dealer!',
    icon: '🃏',
    category: 'table',
    minBet: 0.01,
    maxBet: 1000,
    houseEdge: 0.005,
    maxMultiplier: 3,
    features: ['Classic Rules', 'Strategy Based', 'Low House Edge'],
    isActive: true,
    isFeatured: false,
    isNew: false
  },
  {
    id: 'roulette',
    name: 'Roulette',
    description: 'Place your bets on the roulette table and watch the ball spin. Red or black? Odd or even? Your choice!',
    icon: '🎰',
    category: 'table',
    minBet: 0.01,
    maxBet: 1000,
    houseEdge: 0.027,
    maxMultiplier: 36,
    features: ['European Rules', 'Multiple Bet Types', 'Live Animation'],
    isActive: true,
    isFeatured: false,
    isNew: false
  }
];

// Create placeholder providers for each game
export const createPlaceholderProviders = (): PlaceholderGameProvider[] => {
  return placeholderGames.map(config => new PlaceholderGameProvider(config));
};
