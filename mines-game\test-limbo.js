// Simple test script to verify Limbo game logic
const { LimboGameProvider } = require('./lib/games/limbo/LimboGameProvider.ts');

async function testLimboGame() {
  console.log('🎯 Testing Limbo Game Implementation...\n');
  
  try {
    const limboProvider = new LimboGameProvider();
    
    // Test 1: Game configuration
    console.log('1. Testing game configuration:');
    console.log('   Name:', limboProvider.config.name);
    console.log('   Icon:', limboProvider.config.icon);
    console.log('   Min Bet:', limboProvider.config.minBet);
    console.log('   Max Bet:', limboProvider.config.maxBet);
    console.log('   House Edge:', limboProvider.config.houseEdge);
    console.log('   Max Multiplier:', limboProvider.config.maxMultiplier);
    console.log('   ✅ Configuration looks good\n');
    
    // Test 2: Parameter validation
    console.log('2. Testing parameter validation:');
    const validParams = { betAmount: 1.0, targetMultiplier: 2.0 };
    const invalidParams1 = { betAmount: 0, targetMultiplier: 2.0 };
    const invalidParams2 = { betAmount: 1.0, targetMultiplier: 0.5 };
    
    console.log('   Valid params:', limboProvider.validateGameParams(validParams) ? '✅' : '❌');
    console.log('   Invalid bet amount:', !limboProvider.validateGameParams(invalidParams1) ? '✅' : '❌');
    console.log('   Invalid target multiplier:', !limboProvider.validateGameParams(invalidParams2) ? '✅' : '❌');
    console.log('   ✅ Parameter validation working\n');
    
    // Test 3: Win chance calculation
    console.log('3. Testing win chance calculation:');
    const winChance2x = limboProvider.calculateWinChance(2.0);
    const winChance10x = limboProvider.calculateWinChance(10.0);
    const winChance100x = limboProvider.calculateWinChance(100.0);
    
    console.log(`   2x target: ${winChance2x.toFixed(2)}% win chance`);
    console.log(`   10x target: ${winChance10x.toFixed(2)}% win chance`);
    console.log(`   100x target: ${winChance100x.toFixed(2)}% win chance`);
    console.log('   ✅ Win chances decrease as expected\n');
    
    // Test 4: Game data generation
    console.log('4. Testing game data generation:');
    const gameData = limboProvider.generateGameData({
      userId: 1,
      betAmount: 1.0,
      targetMultiplier: 2.0,
      clientSeed: 'test-seed'
    });
    
    console.log('   Game type:', gameData.game_type);
    console.log('   Bet amount:', gameData.bet_amount);
    console.log('   Target multiplier:', gameData.target_multiplier);
    console.log('   Result multiplier:', gameData.result_multiplier?.toFixed(2));
    console.log('   Status:', gameData.status);
    console.log('   Profit:', gameData.profit?.toFixed(2));
    console.log('   ✅ Game data generation working\n');
    
    // Test 5: Multiple game simulations
    console.log('5. Running simulation (100 games with 2x target):');
    let wins = 0;
    let totalProfit = 0;
    
    for (let i = 0; i < 100; i++) {
      const simGame = limboProvider.generateGameData({
        userId: 1,
        betAmount: 1.0,
        targetMultiplier: 2.0,
        clientSeed: `test-seed-${i}`
      });
      
      if (simGame.status === 'won') {
        wins++;
        totalProfit += simGame.profit || 0;
      } else {
        totalProfit += simGame.profit || 0;
      }
    }
    
    console.log(`   Wins: ${wins}/100 (${wins}%)`);
    console.log(`   Expected win rate: ~${winChance2x.toFixed(1)}%`);
    console.log(`   Total profit: ${totalProfit.toFixed(2)} USDT`);
    console.log(`   Average profit per game: ${(totalProfit / 100).toFixed(3)} USDT`);
    console.log('   ✅ Simulation completed\n');
    
    console.log('🎉 All tests passed! Limbo game is ready to play.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test
testLimboGame();
