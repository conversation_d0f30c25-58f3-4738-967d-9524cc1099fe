import React, { createContext, useContext, useEffect, useState, useRef, ReactNode } from 'react';
import { CrashGameState, CrashGameContextType } from '@/types';
import { useUniversalGame } from './UniversalGameContext';
import { useAuth } from './AuthContext';

const CrashGameContext = createContext<CrashGameContextType | undefined>(undefined);

interface CrashGameProviderProps {
  children: ReactNode;
}

export function CrashGameProvider({ children }: CrashGameProviderProps) {
  const universalGame = useUniversalGame();
  const { user } = useAuth();
  
  // Real-time state for crash game
  const [currentMultiplier, setCurrentMultiplier] = useState(1.0);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [roundPhase, setRoundPhase] = useState<'betting' | 'flying' | 'crashed' | 'waiting'>('betting');
  const [timeUntilNextRound, setTimeUntilNextRound] = useState(5000);
  const [currentCrashPoint, setCurrentCrashPoint] = useState<number | null>(null);

  // Refs for intervals
  const gameLoopRef = useRef<NodeJS.Timeout | null>(null);
  const roundTimerRef = useRef<NodeJS.Timeout | null>(null);
  const roundStartTime = useRef<number | null>(null);
  
  // Ensure we're working with a crash game
  const crashGame = universalGame.currentGameType === 'crash'
    ? universalGame.currentGame as CrashGameState | null
    : null;

  const crashHistory = universalGame.gameHistory.filter(
    game => game.game_type === 'crash'
  ) as CrashGameState[];

  /**
   * Start a new crash game
   */
  const startGame = async (betAmount: number, autoCashOut?: number): Promise<boolean> => {
    // Validate auto cash out minimum
    if (autoCashOut !== undefined && autoCashOut < 1.01) {
      console.error('Auto cash out must be at least 1.01x');
      return false;
    }

    console.log('🎮 CrashGameContext: Starting game with params:', { betAmount, autoCashOut });

    const result = await universalGame.startGame('crash', {
      bet_amount: betAmount,
      auto_cash_out: autoCashOut
    });

    console.log('🎮 CrashGameContext: Game start result:', result);

    // If the game started successfully and we're in betting phase,
    // the betting timer should detect the new game and start flying phase
    if (result && roundPhase === 'betting') {
      console.log('🎮 CrashGameContext: Game started during betting phase, flying phase will start automatically');
    }

    return result;
  };

  /**
   * Cash out the current crash game
   */
  const cashOut = async (): Promise<{ success: boolean; profit: number; multiplier: number }> => {
    try {
      const result = await universalGame.cashOut();
      return {
        success: result.success,
        profit: result.profit,
        multiplier: currentMultiplier
      };
    } catch (error) {
      console.error('Cash out error:', error);
      return {
        success: false,
        profit: 0,
        multiplier: currentMultiplier
      };
    }
  };

  /**
   * Reset the game state
   */
  const resetGame = () => {
    universalGame.resetGame();
    setCurrentMultiplier(1.0);
    setTimeElapsed(0);
    setRoundPhase('waiting');
    setTimeUntilNextRound(0);
  };

  /**
   * Switch to crash game mode
   */
  const switchToCrash = () => {
    universalGame.switchGame('crash');
  };

  /**
   * Load crash game history
   */
  const loadGameHistory = async () => {
    await universalGame.loadGameHistory('crash');
  };

  /**
   * Check if player can cash out
   */
  const canCashOut = (): boolean => {
    return crashGame?.status === 'active' && 
           roundPhase === 'flying' && 
           !crashGame?.cashed_out;
  };

  /**
   * Check if player can place a bet
   */
  const canPlaceBet = (): boolean => {
    return roundPhase === 'betting' && !crashGame;
  };

  /**
   * Get current multiplier
   */
  const getCurrentMultiplier = (): number => {
    return currentMultiplier;
  };

  /**
   * Get time elapsed in current round
   */
  const getTimeElapsed = (): number => {
    return timeElapsed;
  };

  /**
   * Get current round phase
   */
  const getRoundPhase = () => {
    return roundPhase;
  };

  /**
   * Get time until next round
   */
  const getTimeUntilNextRound = (): number => {
    return timeUntilNextRound;
  };

  /**
   * Get crash game statistics
   */
  const getCrashStats = () => {
    if (!crashGame) return null;

    return {
      betAmount: crashGame.bet_amount,
      currentMultiplier: currentMultiplier,
      potentialPayout: crashGame.bet_amount * currentMultiplier,
      phase: roundPhase,
      timeElapsed: timeElapsed,
      autoCashOut: crashGame.auto_cash_out,
      profit: crashGame.profit,
      status: crashGame.status
    };
  };

  /**
   * Generate a random crash point for the round
   */
  const generateCrashPoint = (): number => {
    // Simple random crash point between 1.01x and 50x for testing
    // Most crashes should be low, some high
    const random = Math.random();
    if (random < 0.5) {
      // 50% chance of crash between 1.01x and 2x
      return 1.01 + Math.random() * 0.99;
    } else if (random < 0.8) {
      // 30% chance of crash between 2x and 5x
      return 2 + Math.random() * 3;
    } else if (random < 0.95) {
      // 15% chance of crash between 5x and 10x
      return 5 + Math.random() * 5;
    } else {
      // 5% chance of crash between 10x and 50x
      return 10 + Math.random() * 40;
    }
  };

  /**
   * Start the game loop for real-time updates
   */
  const startGameLoop = () => {
    if (gameLoopRef.current) {
      clearInterval(gameLoopRef.current);
    }

    // For active games, use the game's crash point and start time
    let crashPoint = currentCrashPoint;
    let gameStartTime = roundStartTime.current;

    if (crashGame && crashGame.crash_point) {
      crashPoint = crashGame.crash_point;
      gameStartTime = new Date(crashGame.created_at).getTime();
      setCurrentCrashPoint(crashPoint);
      roundStartTime.current = gameStartTime;
      console.log(`🚀 Resuming active game! Crash point: ${crashPoint.toFixed(2)}x`);
    } else if (!crashPoint) {
      // Generate crash point for demo round (when no active game)
      crashPoint = generateCrashPoint();
      setCurrentCrashPoint(crashPoint);
      roundStartTime.current = Date.now();
      console.log(`🚀 New demo round started! Crash point: ${crashPoint.toFixed(2)}x`);
    }

    gameLoopRef.current = setInterval(() => {
      if (roundPhase === 'flying') {
        const now = Date.now();
        const elapsed = now - (gameStartTime || now);
        setTimeElapsed(elapsed);

        // Calculate new multiplier using exponential growth
        const newMultiplier = Math.pow(1.002, elapsed);
        setCurrentMultiplier(Math.round(newMultiplier * 100) / 100);

        // Check if we've hit the crash point
        if (newMultiplier >= crashPoint) {
          console.log(`💥 CRASHED at ${newMultiplier.toFixed(2)}x (target: ${crashPoint.toFixed(2)}x)`);
          setCurrentMultiplier(crashPoint);
          setRoundPhase('crashed');
          stopGameLoop();

          // If there's an active game, end it as lost
          if (crashGame && !crashGame.cashed_out) {
            // The game crashed, player loses
            console.log('💥 Player lost - game crashed before cash out');
          }

          // Start waiting phase after 3 seconds
          setTimeout(() => {
            startWaitingPhase();
          }, 3000);
        }

        // Check for auto cash out (if there's an active game)
        if (crashGame?.auto_cash_out &&
            newMultiplier >= crashGame.auto_cash_out &&
            !crashGame.cashed_out) {
          cashOut();
        }
      }
    }, 50); // Update every 50ms for smooth animation
  };

  /**
   * Stop the game loop
   */
  const stopGameLoop = () => {
    if (gameLoopRef.current) {
      clearInterval(gameLoopRef.current);
      gameLoopRef.current = null;
    }
  };

  // Note: Round management is now handled server-side via /api/game/crash-round

  /**
   * Initialize crash game rounds - now using server-side round manager
   */
  useEffect(() => {
    // Start polling the server for round state
    const pollRoundState = async () => {
      try {
        const response = await fetch('/api/game/crash-round');
        const data = await response.json();

        if (data.success && data.round) {
          const round = data.round;

          // Update local state based on server round state
          setRoundPhase(round.phase);
          setTimeUntilNextRound(round.timeUntilNextPhase);
          setCurrentMultiplier(round.currentMultiplier);
          setTimeElapsed(round.timeElapsed);

          if (round.crashPoint) {
            setCurrentCrashPoint(round.crashPoint);
          }

          // Reduced logging to improve performance
          if (round.phase !== roundPhase) {
            console.log('🎮 Client: Phase changed to:', round.phase);
          }
        }
      } catch (error) {
        console.error('Failed to fetch round state:', error);
      }
    };

    // Poll every 1000ms to reduce server load significantly
    const pollInterval = setInterval(pollRoundState, 1000);

    // Initial poll
    pollRoundState();

    return () => {
      clearInterval(pollInterval);
      stopGameLoop();
      if (roundTimerRef.current) {
        clearTimeout(roundTimerRef.current);
      }
    };
  }, []);

  /**
   * Handle game state changes
   */
  useEffect(() => {
    if (crashGame) {
      if (crashGame.phase) {
        setRoundPhase(crashGame.phase);
      }
      if (crashGame.time_elapsed !== undefined) {
        setTimeElapsed(crashGame.time_elapsed);
      }
      if (crashGame.current_multiplier) {
        setCurrentMultiplier(crashGame.current_multiplier);
      }
    }
  }, [crashGame]);

  const value: CrashGameContextType = {
    gameState: crashGame,
    gameHistory: crashHistory,
    loading: universalGame.loading,
    error: universalGame.error,
    startGame,
    cashOut,
    resetGame,
    makeMove: cashOut, // Alias for consistency

    // Crash-specific methods
    switchToCrash,
    loadGameHistory,
    canCashOut,
    canPlaceBet,
    getCurrentMultiplier,
    getTimeElapsed,
    getRoundPhase,
    getTimeUntilNextRound,
    getCrashStats
  };

  return (
    <CrashGameContext.Provider value={value}>
      {children}
    </CrashGameContext.Provider>
  );
}

export function useCrashGame() {
  const context = useContext(CrashGameContext);
  if (context === undefined) {
    throw new Error('useCrashGame must be used within a CrashGameProvider');
  }
  return context;
}
