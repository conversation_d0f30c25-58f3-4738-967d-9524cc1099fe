import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LimboGameState } from '@/types';
import { formatCurrency } from '@/lib/utils';
import { Target, TrendingUp, TrendingDown, Zap } from 'lucide-react';

interface LimboDisplayProps {
  gameState: LimboGameState | null;
  loading: boolean;
  lastResult?: {
    betAmount: number;
    targetMultiplier: number;
    resultMultiplier: number;
    won: boolean;
    profit: number;
  } | null;
}

export function LimboDisplay({ gameState, loading, lastResult }: LimboDisplayProps) {
  const [showResult, setShowResult] = useState(false);
  const [animatingResult, setAnimatingResult] = useState(false);

  // Show result animation when game state changes
  useEffect(() => {
    if (gameState?.result_multiplier && gameState.status !== 'active') {
      setAnimatingResult(true);
      setShowResult(true);
      
      // Reset animation after delay
      const timer = setTimeout(() => {
        setAnimatingResult(false);
      }, 2000);
      
      return () => clearTimeout(timer);
    } else {
      setShowResult(false);
      setAnimatingResult(false);
    }
  }, [gameState?.result_multiplier, gameState?.status]);

  const displayResult = gameState || lastResult;
  const isWin = displayResult && (gameState?.status === 'won' || lastResult?.won);
  const isLoss = displayResult && (gameState?.status === 'lost' || (lastResult && !lastResult.won));

  return (
    <div className="space-y-6">
      {/* Main Result Display */}
      <Card className="bg-gray-800/80 border-gray-600 backdrop-blur-sm">
        <CardContent className="p-8">
          <div className="text-center space-y-6">
            {/* Game Title */}
            <div className="flex items-center justify-center space-x-3">
              <Target className="h-8 w-8 text-orange-400" />
              <h2 className="text-3xl font-bold text-white">Limbo</h2>
            </div>

            {/* Result Display */}
            {showResult && displayResult ? (
              <div className="space-y-6">
                {/* Result Multiplier */}
                <div className={`transition-all duration-1000 ${animatingResult ? 'scale-110' : 'scale-100'}`}>
                  <div className="text-6xl font-bold mb-2">
                    <span className={`${isWin ? 'text-green-400' : 'text-red-400'}`}>
                      {(gameState?.result_multiplier || lastResult?.resultMultiplier || 0).toFixed(2)}×
                    </span>
                  </div>
                  <p className="text-gray-400 text-lg">Result Multiplier</p>
                </div>

                {/* Win/Loss Status */}
                <div className={`flex items-center justify-center space-x-2 ${animatingResult ? 'animate-pulse' : ''}`}>
                  {isWin ? (
                    <>
                      <TrendingUp className="h-8 w-8 text-green-400" />
                      <Badge className="bg-green-600/20 text-green-400 text-xl px-4 py-2">
                        YOU WIN!
                      </Badge>
                    </>
                  ) : isLoss ? (
                    <>
                      <TrendingDown className="h-8 w-8 text-red-400" />
                      <Badge className="bg-red-600/20 text-red-400 text-xl px-4 py-2">
                        YOU LOSE
                      </Badge>
                    </>
                  ) : null}
                </div>

                {/* Game Details */}
                <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
                  <div className="bg-gray-700/50 rounded-lg p-4">
                    <p className="text-gray-400 text-sm mb-1">Target</p>
                    <p className="text-white text-lg font-semibold">
                      {(gameState?.target_multiplier || lastResult?.targetMultiplier || 0).toFixed(2)}×
                    </p>
                  </div>
                  
                  <div className="bg-gray-700/50 rounded-lg p-4">
                    <p className="text-gray-400 text-sm mb-1">Bet Amount</p>
                    <p className="text-white text-lg font-semibold">
                      {formatCurrency(gameState?.bet_amount || lastResult?.betAmount || 0)}
                    </p>
                  </div>
                </div>

                {/* Profit/Loss */}
                {(gameState?.profit !== undefined || lastResult?.profit !== undefined) && (
                  <div className="bg-gray-700/30 rounded-lg p-4 max-w-sm mx-auto">
                    <p className="text-gray-400 text-sm mb-1">
                      {isWin ? 'Profit' : 'Loss'}
                    </p>
                    <p className={`text-2xl font-bold ${isWin ? 'text-green-400' : 'text-red-400'}`}>
                      {isWin ? '+' : ''}{formatCurrency(gameState?.profit || lastResult?.profit || 0)}
                    </p>
                  </div>
                )}
              </div>
            ) : (
              /* Waiting State */
              <div className="space-y-6">
                <div className="text-6xl font-bold text-gray-500 mb-2">
                  {loading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-12 h-12 border-4 border-orange-400 border-t-transparent rounded-full animate-spin" />
                    </div>
                  ) : (
                    '?.??×'
                  )}
                </div>
                <p className="text-gray-400 text-lg">
                  {loading ? 'Generating Result...' : 'Place your bet to start'}
                </p>
                
                {!loading && (
                  <div className="flex items-center justify-center space-x-2 text-gray-500">
                    <Zap className="h-5 w-5" />
                    <span className="text-sm">Instant results • Provably fair</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Game Instructions */}
      {!showResult && !loading && (
        <Card className="bg-gray-800/60 border-gray-600">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-white mb-3">How to Play</h3>
            <div className="space-y-2 text-gray-300 text-sm">
              <p>• Choose your bet amount and target multiplier</p>
              <p>• Click "Place Bet" to generate an instant result</p>
              <p>• If the result multiplier ≥ your target, you win!</p>
              <p>• Higher targets = lower win chance but bigger payouts</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Results */}
      {showResult && (
        <Card className="bg-gray-800/60 border-gray-600">
          <CardContent className="p-4">
            <h4 className="text-white font-medium mb-3">Game Summary</h4>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-gray-400 text-xs mb-1">Target</p>
                <p className="text-white text-sm font-medium">
                  {(gameState?.target_multiplier || lastResult?.targetMultiplier || 0).toFixed(2)}×
                </p>
              </div>
              <div>
                <p className="text-gray-400 text-xs mb-1">Result</p>
                <p className={`text-sm font-medium ${isWin ? 'text-green-400' : 'text-red-400'}`}>
                  {(gameState?.result_multiplier || lastResult?.resultMultiplier || 0).toFixed(2)}×
                </p>
              </div>
              <div>
                <p className="text-gray-400 text-xs mb-1">Outcome</p>
                <p className={`text-sm font-medium ${isWin ? 'text-green-400' : 'text-red-400'}`}>
                  {isWin ? 'WIN' : 'LOSS'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
