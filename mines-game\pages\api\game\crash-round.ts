import { NextApiRequest, NextApiResponse } from 'next';
import { initDatabase, gameDb } from '@/lib/database';

// Global round state
let currentRoundState = {
  phase: 'waiting' as 'betting' | 'flying' | 'crashed' | 'waiting',
  startTime: 0,
  bettingEndTime: 0,
  crashPoint: 0,
  roundId: '',
  activeGames: [] as any[]
};

let roundTimer: NodeJS.Timeout | null = null;
let flyingInterval: NodeJS.Timeout | null = null;
let lastRequestTime = 0;
const REQUEST_THROTTLE_MS = 100; // Throttle requests to max 10 per second

// Global singleton flag to prevent multiple initializations across different API calls
declare global {
  var crashRoundManagerInitialized: boolean | undefined;
  var processListenersAdded: boolean | undefined;
  var crashRoundState: any;
}

if (!global.crashRoundManagerInitialized) {
  global.crashRoundManagerInitialized = false;
}

// Store round state globally to persist across hot reloads
if (!global.crashRoundState) {
  global.crashRoundState = {
    phase: 'waiting' as 'betting' | 'flying' | 'crashed' | 'waiting',
    startTime: 0,
    bettingEndTime: 0,
    crashPoint: 0,
    roundId: '',
    activeGames: [] as any[]
  };
}

/**
 * Start a new betting phase
 */
function startBettingPhase() {
  // Clear any existing timer
  if (roundTimer) {
    clearTimeout(roundTimer);
    roundTimer = null;
  }

  console.log('🎮 Server: Starting betting phase');

  global.crashRoundState = {
    phase: 'betting',
    startTime: Date.now(),
    bettingEndTime: Date.now() + 5000, // 5 seconds betting phase
    crashPoint: 0,
    roundId: `crash_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    activeGames: []
  };
  currentRoundState = global.crashRoundState;

  // Schedule flying phase
  roundTimer = setTimeout(() => {
    startFlyingPhase();
  }, 5000);
}

/**
 * Start flying phase
 */
function startFlyingPhase() {
  console.log('🎮 Server: Starting flying phase');

  // Get all active crash games (only initialize DB once)
  if (!gameDb) {
    initDatabase();
  }
  const activeGames = gameDb.getActiveGamesByType('crash');

  if (activeGames.length === 0) {
    console.log('🎮 Server: No active games, skipping to waiting phase');
    startWaitingPhase();
    return;
  }

  console.log(`🎮 Server: Found ${activeGames.length} active crash games`);

  // Generate crash point for this round (use the first game's crash point)
  const crashPoint = (activeGames[0] as any).crash_point || generateCrashPoint();

  currentRoundState = {
    ...currentRoundState,
    phase: 'flying',
    startTime: Date.now(),
    crashPoint: crashPoint,
    activeGames: activeGames
  };

  // Update all games to flying phase
  activeGames.forEach(game => {
    if (game.id) {
      gameDb.update(game.id, {
        phase: 'flying',
        time_elapsed: 0
      });
    }
  });

  // Start the flying simulation
  simulateFlying();
}

/**
 * Simulate the flying phase
 */
function simulateFlying() {
  // Clear any existing flying interval
  if (flyingInterval) {
    clearInterval(flyingInterval);
  }

  flyingInterval = setInterval(() => {
    const timeElapsed = Date.now() - currentRoundState.startTime;
    const currentMultiplier = Math.pow(1.002, timeElapsed / 100);

    // Update all active games
    currentRoundState.activeGames.forEach((game, index) => {
      if (game.status === 'active' && !game.cashed_out) {
        // Check for auto cash out
        if (game.auto_cash_out && currentMultiplier >= game.auto_cash_out) {
          console.log(`🎮 Server: Auto cash out triggered for game ${game.id} at ${game.auto_cash_out.toFixed(2)}x`);

          const profit = game.bet_amount * game.auto_cash_out - game.bet_amount;
          gameDb.update(game.id, {
            status: 'cashed_out',
            current_multiplier: game.auto_cash_out,
            profit: profit,
            cash_out_at: game.auto_cash_out,
            cashed_out: true,
            time_elapsed: timeElapsed
          });

          // Mark game as cashed out in our local state to prevent duplicate updates
          currentRoundState.activeGames[index].cashed_out = true;
          currentRoundState.activeGames[index].status = 'cashed_out';
        } else {
          // Update current multiplier
          gameDb.update(game.id, {
            current_multiplier: currentMultiplier,
            time_elapsed: timeElapsed
          });
        }
      }
    });

    // Check if we've hit the crash point
    if (currentMultiplier >= currentRoundState.crashPoint) {
      console.log(`🎮 Server: CRASHED at ${currentRoundState.crashPoint.toFixed(2)}x`);
      if (flyingInterval) {
        clearInterval(flyingInterval);
        flyingInterval = null;
      }

      // Update all remaining active games as crashed
      currentRoundState.activeGames.forEach(game => {
        if (game.status === 'active' && !game.cashed_out) {
          gameDb.update(game.id, {
            status: 'lost',
            current_multiplier: currentRoundState.crashPoint,
            profit: -game.bet_amount,
            time_elapsed: timeElapsed,
            phase: 'crashed'
          });
        }
      });

      currentRoundState.phase = 'crashed';

      // Start waiting phase after 3 seconds
      setTimeout(() => {
        startWaitingPhase();
      }, 3000);
    }
  }, 50); // Update every 50ms
}

/**
 * Start waiting phase
 */
function startWaitingPhase() {
  // Clear any existing timers
  if (roundTimer) {
    clearTimeout(roundTimer);
    roundTimer = null;
  }
  if (flyingInterval) {
    clearInterval(flyingInterval);
    flyingInterval = null;
  }

  console.log('🎮 Server: Starting waiting phase');

  currentRoundState = {
    ...currentRoundState,
    phase: 'waiting',
    startTime: Date.now()
  };

  // Only start new betting phase if there might be active games
  // Check for active games before starting next round
  roundTimer = setTimeout(() => {
    if (!gameDb) {
      initDatabase();
    }
    const activeGames = gameDb.getActiveGamesByType('crash');
    if (activeGames.length > 0) {
      startBettingPhase();
    } else {
      // No active games, wait longer before checking again
      console.log('🎮 Server: No active crash games, extending waiting phase');
      startWaitingPhase();
    }
  }, 10000); // Wait 10 seconds instead of 3 when no active games
}

/**
 * Generate crash point
 */
function generateCrashPoint(): number {
  const random = Math.random();
  const houseEdge = 0.04;
  const crashPoint = Math.max(1.01, (1 - houseEdge) / random);
  return Math.min(crashPoint, 1000); // Cap at 1000x
}

// Initialize the round system only once globally
function initializeRoundManager() {
  if (!global.crashRoundManagerInitialized) {
    global.crashRoundManagerInitialized = true;
    console.log('🎮 Server: Initializing crash round manager (SINGLETON)');
    // Initialize database once
    initDatabase();
    startBettingPhase();
  }
}

// Cleanup function for graceful shutdown
function cleanup() {
  if (roundTimer) {
    clearTimeout(roundTimer);
    roundTimer = null;
  }
  if (flyingInterval) {
    clearInterval(flyingInterval);
    flyingInterval = null;
  }
  global.crashRoundManagerInitialized = false;
}

// Handle process termination (only add listeners once)
if (!global.processListenersAdded) {
  global.processListenersAdded = true;
  process.on('SIGTERM', cleanup);
  process.on('SIGINT', cleanup);
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  // Throttle requests to prevent spam
  const now = Date.now();
  if (now - lastRequestTime < REQUEST_THROTTLE_MS) {
    // Return cached state for rapid requests
    const timeUntilNextPhase = currentRoundState.phase === 'betting'
      ? Math.max(0, currentRoundState.bettingEndTime - now)
      : currentRoundState.phase === 'waiting'
      ? Math.max(0, 3000 - (now - currentRoundState.startTime))
      : 0;

    const currentMultiplier = currentRoundState.phase === 'flying'
      ? Math.pow(1.002, (now - currentRoundState.startTime) / 100)
      : 1.0;

    return res.status(200).json({
      success: true,
      round: {
        phase: currentRoundState.phase,
        timeUntilNextPhase,
        currentMultiplier: Math.min(currentMultiplier, currentRoundState.crashPoint || 1000),
        crashPoint: currentRoundState.phase === 'crashed' ? currentRoundState.crashPoint : null,
        roundId: currentRoundState.roundId,
        timeElapsed: currentRoundState.phase === 'flying' ? now - currentRoundState.startTime : 0
      }
    });
  }

  lastRequestTime = now;

  // Initialize round manager on first request
  initializeRoundManager();

  // Return current round state
  const timeUntilNextPhase = currentRoundState.phase === 'betting'
    ? Math.max(0, currentRoundState.bettingEndTime - now)
    : currentRoundState.phase === 'waiting'
    ? Math.max(0, 3000 - (now - currentRoundState.startTime))
    : 0;

  const currentMultiplier = currentRoundState.phase === 'flying'
    ? Math.pow(1.002, (now - currentRoundState.startTime) / 100)
    : 1.0;

  res.status(200).json({
    success: true,
    round: {
      phase: currentRoundState.phase,
      timeUntilNextPhase,
      currentMultiplier: Math.min(currentMultiplier, currentRoundState.crashPoint || 1000),
      crashPoint: currentRoundState.phase === 'crashed' ? currentRoundState.crashPoint : null,
      roundId: currentRoundState.roundId,
      timeElapsed: currentRoundState.phase === 'flying' ? now - currentRoundState.startTime : 0
    }
  });
}
