"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/game/crash-round";
exports.ids = ["pages/api/game/crash-round"];
exports.modules = {

/***/ "(api-node)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   gameDb: () => (/* binding */ gameDb),\n/* harmony export */   gameSessionDb: () => (/* binding */ gameSessionDb),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   initDatabase: () => (/* binding */ initDatabase),\n/* harmony export */   leaderboardDb: () => (/* binding */ leaderboardDb),\n/* harmony export */   transactionDb: () => (/* binding */ transactionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb),\n/* harmony export */   userStatsDb: () => (/* binding */ userStatsDb)\n/* harmony export */ });\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(better_sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Database instance\nlet db = null;\n/**\n * Initialize database connection and create tables\n */ function initDatabase() {\n    if (db) return db;\n    const dbPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data', 'mines.db');\n    try {\n        // Ensure data directory exists\n        const fs = __webpack_require__(/*! fs */ \"fs\");\n        const dataDir = path__WEBPACK_IMPORTED_MODULE_1___default().dirname(dbPath);\n        if (!fs.existsSync(dataDir)) {\n            fs.mkdirSync(dataDir, {\n                recursive: true\n            });\n        }\n        db = new (better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default())(dbPath);\n        // Enable WAL mode for better performance\n        db.pragma('journal_mode = WAL');\n        db.pragma('synchronous = NORMAL');\n        db.pragma('cache_size = 1000000');\n        db.pragma('temp_store = memory');\n        createTables();\n        console.log('Database initialized successfully');\n        return db;\n    } catch (error) {\n        console.error('Failed to initialize database:', error);\n        throw error;\n    }\n}\n/**\n * Create database tables\n */ function createTables() {\n    if (!db) throw new Error('Database not initialized');\n    // Users table\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS users (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      username TEXT UNIQUE NOT NULL,\n      email TEXT UNIQUE NOT NULL,\n      password_hash TEXT NOT NULL,\n      usdt_balance REAL DEFAULT 0.0,\n      ltc_balance REAL DEFAULT 0.0,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n    // Check if games table exists and what schema it has\n    const tableExists = db.prepare(\"SELECT name FROM sqlite_master WHERE type='table' AND name='games'\").get();\n    if (tableExists) {\n        // Check if we need to migrate old games table\n        const tableInfo = db.prepare(\"PRAGMA table_info(games)\").all();\n        const hasOldColumns = tableInfo.some((col)=>[\n                'grid_size',\n                'mine_count',\n                'revealed_cells',\n                'mine_positions'\n            ].includes(col.name));\n        const hasGameType = tableInfo.some((col)=>col.name === 'game_type');\n        const hasGameData = tableInfo.some((col)=>col.name === 'game_data');\n        if (hasOldColumns || !hasGameType || !hasGameData) {\n            console.log('🔄 Migrating games table to support multiple game types...');\n            migrateGamesTable();\n        }\n    } else {\n        // Create new table with correct schema\n        console.log('📋 Creating new games table with multi-game support...');\n        db.exec(`\n      CREATE TABLE games (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        game_type TEXT NOT NULL DEFAULT 'mines',\n        bet_amount REAL NOT NULL,\n        current_multiplier REAL DEFAULT 1.0,\n        status TEXT CHECK(status IN ('active', 'won', 'lost', 'cashed_out', 'cancelled')) DEFAULT 'active',\n        server_seed TEXT NOT NULL,\n        client_seed TEXT NOT NULL,\n        server_seed_hash TEXT DEFAULT '',\n        profit REAL DEFAULT 0.0,\n        game_data TEXT DEFAULT '{}',\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n    }\n    // Transactions table\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS transactions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      game_id INTEGER,\n      type TEXT CHECK(type IN ('deposit', 'withdraw', 'bet', 'win')) NOT NULL,\n      currency TEXT CHECK(currency IN ('USDT', 'LTC')) NOT NULL,\n      amount REAL NOT NULL,\n      status TEXT CHECK(status IN ('pending', 'completed', 'failed')) DEFAULT 'pending',\n      transaction_hash TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (user_id) REFERENCES users (id),\n      FOREIGN KEY (game_id) REFERENCES games (id)\n    )\n  `);\n    // Phase 2: Enhanced tables for leaderboards, statistics, and sessions\n    // User Statistics table - tracks detailed user performance\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS user_statistics (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      game_type TEXT NOT NULL,\n      total_games INTEGER DEFAULT 0,\n      total_wins INTEGER DEFAULT 0,\n      total_losses INTEGER DEFAULT 0,\n      total_wagered REAL DEFAULT 0.0,\n      total_profit REAL DEFAULT 0.0,\n      biggest_win REAL DEFAULT 0.0,\n      biggest_loss REAL DEFAULT 0.0,\n      highest_multiplier REAL DEFAULT 0.0,\n      current_streak INTEGER DEFAULT 0,\n      best_win_streak INTEGER DEFAULT 0,\n      best_loss_streak INTEGER DEFAULT 0,\n      last_played DATETIME,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (user_id) REFERENCES users (id),\n      UNIQUE(user_id, game_type)\n    )\n  `);\n    // Leaderboards table - tracks top performers\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS leaderboards (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      username TEXT NOT NULL,\n      game_type TEXT NOT NULL,\n      category TEXT NOT NULL, -- 'profit', 'multiplier', 'streak', 'volume'\n      value REAL NOT NULL,\n      rank_position INTEGER,\n      period TEXT NOT NULL, -- 'daily', 'weekly', 'monthly', 'all_time'\n      period_start DATETIME NOT NULL,\n      period_end DATETIME NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (user_id) REFERENCES users (id),\n      UNIQUE(user_id, game_type, category, period, period_start)\n    )\n  `);\n    // Game Sessions table - tracks user gaming sessions\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS game_sessions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      session_start DATETIME DEFAULT CURRENT_TIMESTAMP,\n      session_end DATETIME,\n      total_games INTEGER DEFAULT 0,\n      total_wagered REAL DEFAULT 0.0,\n      total_profit REAL DEFAULT 0.0,\n      games_won INTEGER DEFAULT 0,\n      games_lost INTEGER DEFAULT 0,\n      biggest_win REAL DEFAULT 0.0,\n      biggest_loss REAL DEFAULT 0.0,\n      is_active BOOLEAN DEFAULT 1,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (user_id) REFERENCES users (id)\n    )\n  `);\n    // Achievements table - tracks user achievements and badges\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS achievements (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      code TEXT UNIQUE NOT NULL,\n      name TEXT NOT NULL,\n      description TEXT NOT NULL,\n      icon TEXT NOT NULL,\n      category TEXT NOT NULL, -- 'wins', 'profit', 'streak', 'volume', 'special'\n      requirement_type TEXT NOT NULL, -- 'count', 'value', 'streak'\n      requirement_value REAL NOT NULL,\n      reward_type TEXT, -- 'badge', 'bonus', 'title'\n      reward_value REAL DEFAULT 0.0,\n      is_active BOOLEAN DEFAULT 1,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n    // User Achievements table - tracks which achievements users have earned\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS user_achievements (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      achievement_id INTEGER NOT NULL,\n      earned_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      progress REAL DEFAULT 0.0,\n      is_completed BOOLEAN DEFAULT 0,\n      FOREIGN KEY (user_id) REFERENCES users (id),\n      FOREIGN KEY (achievement_id) REFERENCES achievements (id),\n      UNIQUE(user_id, achievement_id)\n    )\n  `);\n    // Create indexes for better performance\n    db.exec(`\n    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);\n    CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);\n    CREATE INDEX IF NOT EXISTS idx_games_user_id ON games(user_id);\n    CREATE INDEX IF NOT EXISTS idx_games_status ON games(status);\n    CREATE INDEX IF NOT EXISTS idx_games_type ON games(game_type);\n    CREATE INDEX IF NOT EXISTS idx_games_user_type ON games(user_id, game_type);\n    CREATE INDEX IF NOT EXISTS idx_games_user_status ON games(user_id, status);\n    CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);\n    CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);\n\n    -- Phase 2 indexes\n    CREATE INDEX IF NOT EXISTS idx_user_statistics_user_game ON user_statistics(user_id, game_type);\n    CREATE INDEX IF NOT EXISTS idx_leaderboards_game_category ON leaderboards(game_type, category);\n    CREATE INDEX IF NOT EXISTS idx_leaderboards_period ON leaderboards(period, period_start);\n    CREATE INDEX IF NOT EXISTS idx_leaderboards_rank ON leaderboards(rank_position);\n    CREATE INDEX IF NOT EXISTS idx_game_sessions_user_active ON game_sessions(user_id, is_active);\n    CREATE INDEX IF NOT EXISTS idx_game_sessions_start ON game_sessions(session_start);\n    CREATE INDEX IF NOT EXISTS idx_achievements_category ON achievements(category);\n    CREATE INDEX IF NOT EXISTS idx_user_achievements_user ON user_achievements(user_id);\n    CREATE INDEX IF NOT EXISTS idx_user_achievements_completed ON user_achievements(is_completed);\n  `);\n    // Create triggers for updated_at\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_users_timestamp\n    AFTER UPDATE ON users\n    BEGIN\n      UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_games_timestamp\n    AFTER UPDATE ON games\n    BEGIN\n      UPDATE games SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n    // Phase 2 triggers\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_user_statistics_timestamp\n    AFTER UPDATE ON user_statistics\n    BEGIN\n      UPDATE user_statistics SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_leaderboards_timestamp\n    AFTER UPDATE ON leaderboards\n    BEGIN\n      UPDATE leaderboards SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_game_sessions_timestamp\n    AFTER UPDATE ON game_sessions\n    BEGIN\n      UPDATE game_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n}\n/**\n * Migrate old games table to support multiple game types\n */ function migrateGamesTable() {\n    if (!db) throw new Error('Database not initialized');\n    try {\n        // Check if old columns exist\n        const tableInfo = db.prepare(\"PRAGMA table_info(games)\").all();\n        const hasOldColumns = tableInfo.some((col)=>[\n                'grid_size',\n                'mine_count',\n                'revealed_cells',\n                'mine_positions'\n            ].includes(col.name));\n        if (hasOldColumns) {\n            console.log('📦 Migrating existing mines games...');\n            // Disable foreign key constraints during migration\n            db.exec('PRAGMA foreign_keys = OFF');\n            // First, backup existing data\n            const existingGames = db.prepare(`\n        SELECT * FROM games\n      `).all();\n            console.log(`📋 Found ${existingGames.length} existing games to migrate`);\n            // Create new table with correct schema (without foreign key for now)\n            db.exec(`\n        CREATE TABLE games_new (\n          id INTEGER PRIMARY KEY AUTOINCREMENT,\n          user_id INTEGER NOT NULL,\n          game_type TEXT NOT NULL DEFAULT 'mines',\n          bet_amount REAL NOT NULL,\n          current_multiplier REAL DEFAULT 1.0,\n          status TEXT CHECK(status IN ('active', 'won', 'lost', 'cashed_out', 'cancelled')) DEFAULT 'active',\n          server_seed TEXT NOT NULL,\n          client_seed TEXT NOT NULL,\n          server_seed_hash TEXT DEFAULT '',\n          profit REAL DEFAULT 0.0,\n          game_data TEXT DEFAULT '{}',\n          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n        )\n      `);\n            // Migrate data to new table\n            for (const game of existingGames){\n                const gameData = {\n                    grid_size: game.grid_size || 25,\n                    mine_count: game.mine_count,\n                    revealed_cells: JSON.parse(game.revealed_cells || '[]'),\n                    mine_positions: JSON.parse(game.mine_positions || '[]')\n                };\n                db.prepare(`\n          INSERT INTO games_new (\n            id, user_id, game_type, bet_amount, current_multiplier,\n            status, server_seed, client_seed, server_seed_hash, profit,\n            game_data, created_at, updated_at\n          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n        `).run(game.id, game.user_id, 'mines', game.bet_amount, game.current_multiplier, game.status, game.server_seed, game.client_seed, game.server_seed_hash || '', game.profit, JSON.stringify(gameData), game.created_at, game.updated_at);\n            }\n            // Drop old table and rename new one\n            db.exec(`DROP TABLE games`);\n            db.exec(`ALTER TABLE games_new RENAME TO games`);\n            // Recreate indexes\n            db.exec(`\n        CREATE INDEX IF NOT EXISTS idx_games_user_id ON games(user_id);\n        CREATE INDEX IF NOT EXISTS idx_games_status ON games(status);\n        CREATE INDEX IF NOT EXISTS idx_games_type ON games(game_type);\n        CREATE INDEX IF NOT EXISTS idx_games_user_type ON games(user_id, game_type);\n        CREATE INDEX IF NOT EXISTS idx_games_user_status ON games(user_id, status);\n      `);\n            // Recreate trigger\n            db.exec(`\n        CREATE TRIGGER IF NOT EXISTS update_games_timestamp\n        AFTER UPDATE ON games\n        BEGIN\n          UPDATE games SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n        END;\n      `);\n            // Re-enable foreign key constraints\n            db.exec('PRAGMA foreign_keys = ON');\n            console.log(`✅ Migrated ${existingGames.length} mines games to new schema`);\n        }\n        console.log('✅ Games table migration completed');\n    } catch (error) {\n        console.error('❌ Games table migration failed:', error);\n        throw error;\n    }\n}\n/**\n * Get database instance\n */ function getDatabase() {\n    if (!db) {\n        return initDatabase();\n    }\n    return db;\n}\n/**\n * Close database connection\n */ function closeDatabase() {\n    if (db) {\n        db.close();\n        db = null;\n    }\n}\n/**\n * User database operations\n */ const userDb = {\n    create: (username, email, passwordHash)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      INSERT INTO users (username, email, password_hash)\n      VALUES (?, ?, ?)\n    `);\n        const result = stmt.run(username, email, passwordHash);\n        return userDb.findById(result.lastInsertRowid);\n    },\n    findById: (id)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM users WHERE id = ?');\n        return stmt.get(id);\n    },\n    findByEmail: (email)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM users WHERE email = ?');\n        return stmt.get(email);\n    },\n    findByUsername: (username)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM users WHERE username = ?');\n        return stmt.get(username);\n    },\n    updateBalance: (userId, currency, amount)=>{\n        const db = getDatabase();\n        const column = currency === 'USDT' ? 'usdt_balance' : 'ltc_balance';\n        const stmt = db.prepare(`UPDATE users SET ${column} = ? WHERE id = ?`);\n        const result = stmt.run(amount, userId);\n        return result.changes > 0;\n    },\n    addToBalance: (userId, currency, amount)=>{\n        const db = getDatabase();\n        const column = currency === 'USDT' ? 'usdt_balance' : 'ltc_balance';\n        const stmt = db.prepare(`UPDATE users SET ${column} = ${column} + ? WHERE id = ?`);\n        const result = stmt.run(amount, userId);\n        return result.changes > 0;\n    }\n};\n/**\n * Game database operations\n */ const gameDb = {\n    create: (gameData)=>{\n        const db = getDatabase();\n        // Extract game-specific data\n        const { game_type, user_id, bet_amount, current_multiplier, status, server_seed, client_seed, profit, ...specificData } = gameData;\n        const stmt = db.prepare(`\n      INSERT INTO games (\n        user_id, game_type, bet_amount, current_multiplier,\n        status, server_seed, client_seed, server_seed_hash, profit, game_data\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(user_id, game_type, bet_amount, current_multiplier, status, server_seed, client_seed, '', profit, JSON.stringify(specificData));\n        return gameDb.findById(result.lastInsertRowid);\n    },\n    findById: (id)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM games WHERE id = ?');\n        const game = stmt.get(id);\n        if (!game) return null;\n        return gameDb.parseGameData(game);\n    },\n    update: (id, updates)=>{\n        const db = getDatabase();\n        // Separate base fields from game-specific data\n        const { game_type, user_id, bet_amount, current_multiplier, status, server_seed, client_seed, profit, ...specificData } = updates;\n        const baseFields = {};\n        if (bet_amount !== undefined) baseFields.bet_amount = bet_amount;\n        if (current_multiplier !== undefined) baseFields.current_multiplier = current_multiplier;\n        if (status !== undefined) baseFields.status = status;\n        if (profit !== undefined) baseFields.profit = profit;\n        // If there's game-specific data, update game_data field\n        if (Object.keys(specificData).length > 0) {\n            // Get current game data and merge\n            const currentGame = gameDb.findById(id);\n            if (currentGame) {\n                const currentSpecificData = gameDb.extractGameSpecificData(currentGame);\n                const mergedData = {\n                    ...currentSpecificData,\n                    ...specificData\n                };\n                baseFields.game_data = JSON.stringify(mergedData);\n            }\n        }\n        if (Object.keys(baseFields).length === 0) return false;\n        const fields = Object.keys(baseFields);\n        const setClause = fields.map((field)=>`${field} = ?`).join(', ');\n        const values = fields.map((field)=>baseFields[field]);\n        const stmt = db.prepare(`UPDATE games SET ${setClause} WHERE id = ?`);\n        const result = stmt.run(...values, id);\n        return result.changes > 0;\n    },\n    findActiveByUserId: (userId)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM games WHERE user_id = ? AND status = ? ORDER BY created_at DESC LIMIT 1');\n        const game = stmt.get(userId, 'active');\n        if (!game) return null;\n        return gameDb.parseGameData(game);\n    },\n    findByUserId: (userId, limit = 50, gameType)=>{\n        const db = getDatabase();\n        let query = 'SELECT * FROM games WHERE user_id = ?';\n        const params = [\n            userId\n        ];\n        if (gameType) {\n            query += ' AND game_type = ?';\n            params.push(gameType);\n        }\n        query += ' ORDER BY created_at DESC LIMIT ?';\n        params.push(limit);\n        const stmt = db.prepare(query);\n        const games = stmt.all(...params);\n        return games.map((game)=>gameDb.parseGameData(game));\n    },\n    getActiveGamesByType: (gameType)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM games WHERE game_type = ? AND status = ? ORDER BY created_at DESC');\n        const games = stmt.all(gameType, 'active');\n        return games.map((game)=>gameDb.parseGameData(game));\n    },\n    // Helper method to parse game data from database\n    parseGameData: (dbGame)=>{\n        const gameData = JSON.parse(dbGame.game_data || '{}');\n        return {\n            ...dbGame,\n            ...gameData\n        };\n    },\n    // Helper method to extract game-specific data\n    extractGameSpecificData: (gameState)=>{\n        const { id, user_id, game_type, bet_amount, current_multiplier, status, server_seed, client_seed, profit, created_at, updated_at, ...specificData } = gameState;\n        return specificData;\n    }\n};\n/**\n * Transaction database operations\n */ const transactionDb = {\n    create: (transactionData)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      INSERT INTO transactions (user_id, game_id, type, currency, amount, status, transaction_hash)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(transactionData.user_id, transactionData.game_id || null, transactionData.type, transactionData.currency, transactionData.amount, transactionData.status, transactionData.transaction_hash || null);\n        return transactionDb.findById(result.lastInsertRowid);\n    },\n    findById: (id)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM transactions WHERE id = ?');\n        return stmt.get(id);\n    },\n    findByUserId: (userId, limit = 50)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT ?');\n        return stmt.all(userId, limit);\n    },\n    updateStatus: (id, status)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('UPDATE transactions SET status = ? WHERE id = ?');\n        const result = stmt.run(status, id);\n        return result.changes > 0;\n    }\n};\n/**\n * Phase 2: User Statistics database operations\n */ const userStatsDb = {\n    create: (userId, gameType)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      INSERT INTO user_statistics (user_id, game_type)\n      VALUES (?, ?)\n    `);\n        const result = stmt.run(userId, gameType);\n        return userStatsDb.findByUserAndGame(userId, gameType);\n    },\n    findByUserAndGame: (userId, gameType)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM user_statistics WHERE user_id = ? AND game_type = ?');\n        return stmt.get(userId, gameType);\n    },\n    findByUser: (userId)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM user_statistics WHERE user_id = ?');\n        return stmt.all(userId);\n    },\n    updateStats: (userId, gameType, updates)=>{\n        const db = getDatabase();\n        const existing = userStatsDb.findByUserAndGame(userId, gameType);\n        if (!existing) {\n            userStatsDb.create(userId, gameType);\n        }\n        const fields = Object.keys(updates).map((key)=>`${key} = ?`).join(', ');\n        const values = Object.values(updates);\n        const stmt = db.prepare(`\n      UPDATE user_statistics\n      SET ${fields}, last_played = CURRENT_TIMESTAMP\n      WHERE user_id = ? AND game_type = ?\n    `);\n        const result = stmt.run(...values, userId, gameType);\n        return result.changes > 0;\n    },\n    incrementStats: (userId, gameType, increments)=>{\n        const db = getDatabase();\n        const existing = userStatsDb.findByUserAndGame(userId, gameType);\n        if (!existing) {\n            userStatsDb.create(userId, gameType);\n        }\n        const fields = Object.keys(increments).map((key)=>`${key} = ${key} + ?`).join(', ');\n        const values = Object.values(increments);\n        const stmt = db.prepare(`\n      UPDATE user_statistics\n      SET ${fields}, last_played = CURRENT_TIMESTAMP\n      WHERE user_id = ? AND game_type = ?\n    `);\n        const result = stmt.run(...values, userId, gameType);\n        return result.changes > 0;\n    }\n};\n/**\n * Phase 2: Leaderboards database operations\n */ const leaderboardDb = {\n    updateEntry: (userId, username, gameType, category, value, period)=>{\n        const db = getDatabase();\n        // Calculate period dates\n        const now = new Date();\n        let periodStart, periodEnd;\n        switch(period){\n            case 'daily':\n                periodStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n                periodEnd = new Date(periodStart.getTime() + 24 * 60 * 60 * 1000);\n                break;\n            case 'weekly':\n                const dayOfWeek = now.getDay();\n                periodStart = new Date(now.getTime() - dayOfWeek * 24 * 60 * 60 * 1000);\n                periodStart.setHours(0, 0, 0, 0);\n                periodEnd = new Date(periodStart.getTime() + 7 * 24 * 60 * 60 * 1000);\n                break;\n            case 'monthly':\n                periodStart = new Date(now.getFullYear(), now.getMonth(), 1);\n                periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 1);\n                break;\n            default:\n                periodStart = new Date(2024, 0, 1); // Platform start date\n                periodEnd = new Date(2099, 11, 31); // Far future\n        }\n        const stmt = db.prepare(`\n      INSERT OR REPLACE INTO leaderboards\n      (user_id, username, game_type, category, value, period, period_start, period_end)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(userId, username, gameType, category, value, period, periodStart.toISOString(), periodEnd.toISOString());\n        return result.changes > 0;\n    },\n    getLeaderboard: (gameType, category, period, limit = 10)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      SELECT * FROM leaderboards\n      WHERE game_type = ? AND category = ? AND period = ?\n      ORDER BY value DESC\n      LIMIT ?\n    `);\n        return stmt.all(gameType, category, period, limit);\n    },\n    getUserRank: (userId, gameType, category, period)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      SELECT COUNT(*) + 1 as rank FROM leaderboards\n      WHERE game_type = ? AND category = ? AND period = ? AND value > (\n        SELECT COALESCE(value, 0) FROM leaderboards\n        WHERE user_id = ? AND game_type = ? AND category = ? AND period = ?\n      )\n    `);\n        const result = stmt.get(gameType, category, period, userId, gameType, category, period);\n        return result?.rank || 0;\n    }\n};\n/**\n * Phase 2: Game Sessions database operations\n */ const gameSessionDb = {\n    startSession: (userId)=>{\n        const db = getDatabase();\n        // End any existing active session\n        gameSessionDb.endActiveSession(userId);\n        const stmt = db.prepare(`\n      INSERT INTO game_sessions (user_id)\n      VALUES (?)\n    `);\n        const result = stmt.run(userId);\n        return gameSessionDb.findById(result.lastInsertRowid);\n    },\n    findById: (id)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM game_sessions WHERE id = ?');\n        return stmt.get(id);\n    },\n    findActiveSession: (userId)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM game_sessions WHERE user_id = ? AND is_active = 1 ORDER BY session_start DESC LIMIT 1');\n        return stmt.get(userId);\n    },\n    updateSession: (sessionId, updates)=>{\n        const db = getDatabase();\n        const fields = Object.keys(updates).map((key)=>`${key} = ?`).join(', ');\n        const values = Object.values(updates);\n        const stmt = db.prepare(`UPDATE game_sessions SET ${fields} WHERE id = ?`);\n        const result = stmt.run(...values, sessionId);\n        return result.changes > 0;\n    },\n    endActiveSession: (userId)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      UPDATE game_sessions\n      SET is_active = 0, session_end = CURRENT_TIMESTAMP\n      WHERE user_id = ? AND is_active = 1\n    `);\n        const result = stmt.run(userId);\n        return result.changes > 0;\n    },\n    getUserSessions: (userId, limit = 20)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      SELECT * FROM game_sessions\n      WHERE user_id = ?\n      ORDER BY session_start DESC\n      LIMIT ?\n    `);\n        return stmt.all(userId, limit);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./lib/database.ts\n");

/***/ }),

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fgame%2Fcrash-round&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cgame%5Ccrash-round.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fgame%2Fcrash-round&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cgame%5Ccrash-round.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_game_crash_round_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\game\\crash-round.ts */ \"(api-node)/./pages/api/game/crash-round.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_game_crash_round_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_game_crash_round_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/game/crash-round\",\n        pathname: \"/api/game/crash-round\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_game_crash_round_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fgame%2Fcrash-round&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cgame%5Ccrash-round.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/game/crash-round.ts":
/*!***************************************!*\
  !*** ./pages/api/game/crash-round.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/database */ \"(api-node)/./lib/database.ts\");\n\n// Global round state\nlet currentRoundState = {\n    phase: 'waiting',\n    startTime: 0,\n    bettingEndTime: 0,\n    crashPoint: 0,\n    roundId: '',\n    activeGames: []\n};\nlet roundTimer = null;\nlet flyingInterval = null;\nlet lastRequestTime = 0;\nconst REQUEST_THROTTLE_MS = 100; // Throttle requests to max 10 per second\nif (!global.crashRoundManagerInitialized) {\n    global.crashRoundManagerInitialized = false;\n}\n/**\n * Start a new betting phase\n */ function startBettingPhase() {\n    // Clear any existing timer\n    if (roundTimer) {\n        clearTimeout(roundTimer);\n        roundTimer = null;\n    }\n    console.log('🎮 Server: Starting betting phase');\n    currentRoundState = {\n        phase: 'betting',\n        startTime: Date.now(),\n        bettingEndTime: Date.now() + 5000,\n        crashPoint: 0,\n        roundId: `crash_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,\n        activeGames: []\n    };\n    // Schedule flying phase\n    roundTimer = setTimeout(()=>{\n        startFlyingPhase();\n    }, 5000);\n}\n/**\n * Start flying phase\n */ function startFlyingPhase() {\n    console.log('🎮 Server: Starting flying phase');\n    // Get all active crash games (only initialize DB once)\n    if (!_lib_database__WEBPACK_IMPORTED_MODULE_0__.gameDb) {\n        (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.initDatabase)();\n    }\n    const activeGames = _lib_database__WEBPACK_IMPORTED_MODULE_0__.gameDb.getActiveGamesByType('crash');\n    if (activeGames.length === 0) {\n        console.log('🎮 Server: No active games, skipping to waiting phase');\n        startWaitingPhase();\n        return;\n    }\n    console.log(`🎮 Server: Found ${activeGames.length} active crash games`);\n    // Generate crash point for this round (use the first game's crash point)\n    const crashPoint = activeGames[0].crash_point || generateCrashPoint();\n    currentRoundState = {\n        ...currentRoundState,\n        phase: 'flying',\n        startTime: Date.now(),\n        crashPoint: crashPoint,\n        activeGames: activeGames\n    };\n    // Update all games to flying phase\n    activeGames.forEach((game)=>{\n        if (game.id) {\n            _lib_database__WEBPACK_IMPORTED_MODULE_0__.gameDb.update(game.id, {\n                phase: 'flying',\n                time_elapsed: 0\n            });\n        }\n    });\n    // Start the flying simulation\n    simulateFlying();\n}\n/**\n * Simulate the flying phase\n */ function simulateFlying() {\n    // Clear any existing flying interval\n    if (flyingInterval) {\n        clearInterval(flyingInterval);\n    }\n    flyingInterval = setInterval(()=>{\n        const timeElapsed = Date.now() - currentRoundState.startTime;\n        const currentMultiplier = Math.pow(1.002, timeElapsed / 100);\n        // Update all active games\n        currentRoundState.activeGames.forEach((game, index)=>{\n            if (game.status === 'active' && !game.cashed_out) {\n                // Check for auto cash out\n                if (game.auto_cash_out && currentMultiplier >= game.auto_cash_out) {\n                    console.log(`🎮 Server: Auto cash out triggered for game ${game.id} at ${game.auto_cash_out.toFixed(2)}x`);\n                    const profit = game.bet_amount * game.auto_cash_out - game.bet_amount;\n                    _lib_database__WEBPACK_IMPORTED_MODULE_0__.gameDb.update(game.id, {\n                        status: 'cashed_out',\n                        current_multiplier: game.auto_cash_out,\n                        profit: profit,\n                        cash_out_at: game.auto_cash_out,\n                        cashed_out: true,\n                        time_elapsed: timeElapsed\n                    });\n                    // Mark game as cashed out in our local state to prevent duplicate updates\n                    currentRoundState.activeGames[index].cashed_out = true;\n                    currentRoundState.activeGames[index].status = 'cashed_out';\n                } else {\n                    // Update current multiplier\n                    _lib_database__WEBPACK_IMPORTED_MODULE_0__.gameDb.update(game.id, {\n                        current_multiplier: currentMultiplier,\n                        time_elapsed: timeElapsed\n                    });\n                }\n            }\n        });\n        // Check if we've hit the crash point\n        if (currentMultiplier >= currentRoundState.crashPoint) {\n            console.log(`🎮 Server: CRASHED at ${currentRoundState.crashPoint.toFixed(2)}x`);\n            if (flyingInterval) {\n                clearInterval(flyingInterval);\n                flyingInterval = null;\n            }\n            // Update all remaining active games as crashed\n            currentRoundState.activeGames.forEach((game)=>{\n                if (game.status === 'active' && !game.cashed_out) {\n                    _lib_database__WEBPACK_IMPORTED_MODULE_0__.gameDb.update(game.id, {\n                        status: 'lost',\n                        current_multiplier: currentRoundState.crashPoint,\n                        profit: -game.bet_amount,\n                        time_elapsed: timeElapsed,\n                        phase: 'crashed'\n                    });\n                }\n            });\n            currentRoundState.phase = 'crashed';\n            // Start waiting phase after 3 seconds\n            setTimeout(()=>{\n                startWaitingPhase();\n            }, 3000);\n        }\n    }, 50); // Update every 50ms\n}\n/**\n * Start waiting phase\n */ function startWaitingPhase() {\n    // Clear any existing timers\n    if (roundTimer) {\n        clearTimeout(roundTimer);\n        roundTimer = null;\n    }\n    if (flyingInterval) {\n        clearInterval(flyingInterval);\n        flyingInterval = null;\n    }\n    console.log('🎮 Server: Starting waiting phase');\n    currentRoundState = {\n        ...currentRoundState,\n        phase: 'waiting',\n        startTime: Date.now()\n    };\n    // Start new betting phase after 3 seconds\n    roundTimer = setTimeout(()=>{\n        startBettingPhase();\n    }, 3000);\n}\n/**\n * Generate crash point\n */ function generateCrashPoint() {\n    const random = Math.random();\n    const houseEdge = 0.04;\n    const crashPoint = Math.max(1.01, (1 - houseEdge) / random);\n    return Math.min(crashPoint, 1000); // Cap at 1000x\n}\n// Initialize the round system only once globally\nfunction initializeRoundManager() {\n    if (!global.crashRoundManagerInitialized) {\n        global.crashRoundManagerInitialized = true;\n        console.log('🎮 Server: Initializing crash round manager (SINGLETON)');\n        // Initialize database once\n        (0,_lib_database__WEBPACK_IMPORTED_MODULE_0__.initDatabase)();\n        startBettingPhase();\n    }\n}\n// Cleanup function for graceful shutdown\nfunction cleanup() {\n    if (roundTimer) {\n        clearTimeout(roundTimer);\n        roundTimer = null;\n    }\n    if (flyingInterval) {\n        clearInterval(flyingInterval);\n        flyingInterval = null;\n    }\n    global.crashRoundManagerInitialized = false;\n}\n// Handle process termination\nprocess.on('SIGTERM', cleanup);\nprocess.on('SIGINT', cleanup);\nfunction handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            success: false,\n            error: 'Method not allowed'\n        });\n    }\n    // Throttle requests to prevent spam\n    const now = Date.now();\n    if (now - lastRequestTime < REQUEST_THROTTLE_MS) {\n        // Return cached state for rapid requests\n        const timeUntilNextPhase = currentRoundState.phase === 'betting' ? Math.max(0, currentRoundState.bettingEndTime - now) : currentRoundState.phase === 'waiting' ? Math.max(0, 3000 - (now - currentRoundState.startTime)) : 0;\n        const currentMultiplier = currentRoundState.phase === 'flying' ? Math.pow(1.002, (now - currentRoundState.startTime) / 100) : 1.0;\n        return res.status(200).json({\n            success: true,\n            round: {\n                phase: currentRoundState.phase,\n                timeUntilNextPhase,\n                currentMultiplier: Math.min(currentMultiplier, currentRoundState.crashPoint || 1000),\n                crashPoint: currentRoundState.phase === 'crashed' ? currentRoundState.crashPoint : null,\n                roundId: currentRoundState.roundId,\n                timeElapsed: currentRoundState.phase === 'flying' ? now - currentRoundState.startTime : 0\n            }\n        });\n    }\n    lastRequestTime = now;\n    // Initialize round manager on first request\n    initializeRoundManager();\n    // Return current round state\n    const timeUntilNextPhase = currentRoundState.phase === 'betting' ? Math.max(0, currentRoundState.bettingEndTime - now) : currentRoundState.phase === 'waiting' ? Math.max(0, 3000 - (now - currentRoundState.startTime)) : 0;\n    const currentMultiplier = currentRoundState.phase === 'flying' ? Math.pow(1.002, (now - currentRoundState.startTime) / 100) : 1.0;\n    res.status(200).json({\n        success: true,\n        round: {\n            phase: currentRoundState.phase,\n            timeUntilNextPhase,\n            currentMultiplier: Math.min(currentMultiplier, currentRoundState.crashPoint || 1000),\n            crashPoint: currentRoundState.phase === 'crashed' ? currentRoundState.crashPoint : null,\n            roundId: currentRoundState.roundId,\n            timeElapsed: currentRoundState.phase === 'flying' ? now - currentRoundState.startTime : 0\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/game/crash-round.ts\n");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("better-sqlite3");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fgame%2Fcrash-round&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cgame%5Ccrash-round.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();