"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/wallet/deposit";
exports.ids = ["pages/api/wallet/deposit"];
exports.modules = {

/***/ "(api-node)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateRequest: () => (/* binding */ authenticateRequest),\n/* harmony export */   checkRateLimit: () => (/* binding */ checkRateLimit),\n/* harmony export */   clearAuthCookie: () => (/* binding */ clearAuthCookie),\n/* harmony export */   extractTokenFromRequest: () => (/* binding */ extractTokenFromRequest),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getClientIP: () => (/* binding */ getClientIP),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   setAuthCookie: () => (/* binding */ setAuthCookie),\n/* harmony export */   validateLoginData: () => (/* binding */ validateLoginData),\n/* harmony export */   validateRegistrationData: () => (/* binding */ validateRegistrationData),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"jsonwebtoken\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs?cd17\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(api-node)/./lib/database.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([bcryptjs__WEBPACK_IMPORTED_MODULE_1__]);\nbcryptjs__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// JWT secret - in production, this should be in environment variables\nconst JWT_SECRET = \"your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random\" || 0;\nconst JWT_EXPIRES_IN = '7d';\n/**\n * Hash password using bcrypt\n */ async function hashPassword(password) {\n    const saltRounds = 12;\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hash(password, saltRounds);\n}\n/**\n * Verify password against hash\n */ async function verifyPassword(password, hash) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(password, hash);\n}\n/**\n * Generate JWT token for user\n */ function generateToken(user) {\n    const payload = {\n        userId: user.id,\n        username: user.username,\n        email: user.email\n    };\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n}\n/**\n * Verify JWT token and return user data\n */ function verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        return null;\n    }\n}\n/**\n * Extract token from request headers\n */ function extractTokenFromRequest(req) {\n    const authHeader = req.headers.authorization;\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n        return authHeader.substring(7);\n    }\n    // Also check cookies for token\n    const cookieToken = req.cookies.token;\n    if (cookieToken) {\n        return cookieToken;\n    }\n    return null;\n}\n/**\n * Middleware to authenticate requests\n */ function authenticateRequest(req) {\n    const token = extractTokenFromRequest(req);\n    if (!token) {\n        return null;\n    }\n    const decoded = verifyToken(token);\n    if (!decoded || !decoded.userId) {\n        return null;\n    }\n    // Get fresh user data from database\n    const user = _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findById(decoded.userId);\n    return user;\n}\n/**\n * Higher-order function to protect API routes\n */ function withAuth(handler) {\n    return async (req, res)=>{\n        try {\n            const user = authenticateRequest(req);\n            if (!user) {\n                return res.status(401).json({\n                    success: false,\n                    error: 'Authentication required'\n                });\n            }\n            await handler(req, res, user);\n        } catch (error) {\n            console.error('Auth middleware error:', error);\n            res.status(500).json({\n                success: false,\n                error: 'Internal server error'\n            });\n        }\n    };\n}\n/**\n * Validate user registration data\n */ function validateRegistrationData(username, email, password) {\n    // Username validation\n    if (!username || username.length < 3 || username.length > 20) {\n        return 'Username must be between 3 and 20 characters';\n    }\n    if (!/^[a-zA-Z0-9_]+$/.test(username)) {\n        return 'Username can only contain letters, numbers, and underscores';\n    }\n    // Email validation\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!email || !emailRegex.test(email)) {\n        return 'Please provide a valid email address';\n    }\n    // Password validation\n    if (!password || password.length < 8) {\n        return 'Password must be at least 8 characters long';\n    }\n    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/.test(password)) {\n        return 'Password must contain at least one uppercase letter, one lowercase letter, and one number';\n    }\n    return null;\n}\n/**\n * Validate login data\n */ function validateLoginData(email, password) {\n    if (!email || !password) {\n        return 'Email and password are required';\n    }\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(email)) {\n        return 'Please provide a valid email address';\n    }\n    return null;\n}\n/**\n * Register new user\n */ async function registerUser(username, email, password) {\n    try {\n        // Validate input data\n        const validationError = validateRegistrationData(username, email, password);\n        if (validationError) {\n            return {\n                success: false,\n                error: validationError\n            };\n        }\n        // Check if user already exists\n        const existingUserByEmail = _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(email);\n        if (existingUserByEmail) {\n            return {\n                success: false,\n                error: 'Email already registered'\n            };\n        }\n        const existingUserByUsername = _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByUsername(username);\n        if (existingUserByUsername) {\n            return {\n                success: false,\n                error: 'Username already taken'\n            };\n        }\n        // Hash password\n        const passwordHash = await hashPassword(password);\n        // Create user\n        const user = _database__WEBPACK_IMPORTED_MODULE_2__.userDb.create(username, email, passwordHash);\n        // Remove password hash from response\n        const { password_hash, ...userWithoutPassword } = user;\n        return {\n            success: true,\n            user: userWithoutPassword\n        };\n    } catch (error) {\n        console.error('Registration error:', error);\n        return {\n            success: false,\n            error: 'Failed to register user'\n        };\n    }\n}\n/**\n * Login user\n */ async function loginUser(email, password) {\n    try {\n        // Validate input data\n        const validationError = validateLoginData(email, password);\n        if (validationError) {\n            return {\n                success: false,\n                error: validationError\n            };\n        }\n        // Find user by email\n        const user = _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(email);\n        if (!user) {\n            return {\n                success: false,\n                error: 'Invalid email or password'\n            };\n        }\n        // Verify password\n        const isValidPassword = await verifyPassword(password, user.password_hash);\n        if (!isValidPassword) {\n            return {\n                success: false,\n                error: 'Invalid email or password'\n            };\n        }\n        // Generate token\n        const token = generateToken(user);\n        // Remove password hash from response\n        const { password_hash, ...userWithoutPassword } = user;\n        return {\n            success: true,\n            user: userWithoutPassword,\n            token\n        };\n    } catch (error) {\n        console.error('Login error:', error);\n        return {\n            success: false,\n            error: 'Failed to login'\n        };\n    }\n}\n/**\n * Set authentication cookie\n */ function setAuthCookie(res, token) {\n    const isProduction = \"development\" === 'production';\n    res.setHeader('Set-Cookie', [\n        `token=${token}; HttpOnly; Path=/; Max-Age=${7 * 24 * 60 * 60}; SameSite=Strict${isProduction ? '; Secure' : ''}`\n    ]);\n}\n/**\n * Clear authentication cookie\n */ function clearAuthCookie(res) {\n    res.setHeader('Set-Cookie', [\n        'token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict'\n    ]);\n}\n/**\n * Rate limiting for authentication endpoints\n */ const rateLimitMap = new Map();\nfunction checkRateLimit(ip, maxAttempts = 5, windowMs = 15 * 60 * 1000) {\n    const now = Date.now();\n    const record = rateLimitMap.get(ip);\n    if (!record || now > record.resetTime) {\n        rateLimitMap.set(ip, {\n            count: 1,\n            resetTime: now + windowMs\n        });\n        return true;\n    }\n    if (record.count >= maxAttempts) {\n        return false;\n    }\n    record.count++;\n    return true;\n}\n/**\n * Get client IP address\n */ function getClientIP(req) {\n    const forwarded = req.headers['x-forwarded-for'];\n    const ip = forwarded ? Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0] : req.socket.remoteAddress;\n    return ip || 'unknown';\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL2xpYi9hdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBK0I7QUFDRDtBQUdNO0FBRXBDLHNFQUFzRTtBQUN0RSxNQUFNRyxhQUFhQyxrRkFBc0IsSUFBSSxDQUFxRDtBQUNsRyxNQUFNRSxpQkFBaUI7QUFFdkI7O0NBRUMsR0FDTSxlQUFlQyxhQUFhQyxRQUFnQjtJQUNqRCxNQUFNQyxhQUFhO0lBQ25CLE9BQU9SLHFEQUFXLENBQUNPLFVBQVVDO0FBQy9CO0FBRUE7O0NBRUMsR0FDTSxlQUFlRSxlQUFlSCxRQUFnQixFQUFFRSxJQUFZO0lBQ2pFLE9BQU9ULHdEQUFjLENBQUNPLFVBQVVFO0FBQ2xDO0FBRUE7O0NBRUMsR0FDTSxTQUFTRyxjQUFjQyxJQUFVO0lBQ3RDLE1BQU1DLFVBQVU7UUFDZEMsUUFBUUYsS0FBS0csRUFBRTtRQUNmQyxVQUFVSixLQUFLSSxRQUFRO1FBQ3ZCQyxPQUFPTCxLQUFLSyxLQUFLO0lBQ25CO0lBRUEsT0FBT25CLHdEQUFRLENBQUNlLFNBQVNaLFlBQVk7UUFBRWtCLFdBQVdmO0lBQWU7QUFDbkU7QUFFQTs7Q0FFQyxHQUNNLFNBQVNnQixZQUFZQyxLQUFhO0lBQ3ZDLElBQUk7UUFDRixPQUFPdkIsMERBQVUsQ0FBQ3VCLE9BQU9wQjtJQUMzQixFQUFFLE9BQU9zQixPQUFPO1FBQ2QsT0FBTztJQUNUO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLFNBQVNDLHdCQUF3QkMsR0FBbUI7SUFDekQsTUFBTUMsYUFBYUQsSUFBSUUsT0FBTyxDQUFDQyxhQUFhO0lBRTVDLElBQUlGLGNBQWNBLFdBQVdHLFVBQVUsQ0FBQyxZQUFZO1FBQ2xELE9BQU9ILFdBQVdJLFNBQVMsQ0FBQztJQUM5QjtJQUVBLCtCQUErQjtJQUMvQixNQUFNQyxjQUFjTixJQUFJTyxPQUFPLENBQUNYLEtBQUs7SUFDckMsSUFBSVUsYUFBYTtRQUNmLE9BQU9BO0lBQ1Q7SUFFQSxPQUFPO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNNLFNBQVNFLG9CQUFvQlIsR0FBbUI7SUFDckQsTUFBTUosUUFBUUcsd0JBQXdCQztJQUV0QyxJQUFJLENBQUNKLE9BQU87UUFDVixPQUFPO0lBQ1Q7SUFFQSxNQUFNYSxVQUFVZCxZQUFZQztJQUM1QixJQUFJLENBQUNhLFdBQVcsQ0FBQ0EsUUFBUXBCLE1BQU0sRUFBRTtRQUMvQixPQUFPO0lBQ1Q7SUFFQSxvQ0FBb0M7SUFDcEMsTUFBTUYsT0FBT1osNkNBQU1BLENBQUNtQyxRQUFRLENBQUNELFFBQVFwQixNQUFNO0lBQzNDLE9BQU9GO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNNLFNBQVN3QixTQUFTQyxPQUFpRjtJQUN4RyxPQUFPLE9BQU9aLEtBQXFCYTtRQUNqQyxJQUFJO1lBQ0YsTUFBTTFCLE9BQU9xQixvQkFBb0JSO1lBRWpDLElBQUksQ0FBQ2IsTUFBTTtnQkFDVCxPQUFPMEIsSUFBSUMsTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztvQkFDMUJDLFNBQVM7b0JBQ1RsQixPQUFPO2dCQUNUO1lBQ0Y7WUFFQSxNQUFNYyxRQUFRWixLQUFLYSxLQUFLMUI7UUFDMUIsRUFBRSxPQUFPVyxPQUFPO1lBQ2RtQixRQUFRbkIsS0FBSyxDQUFDLDBCQUEwQkE7WUFDeENlLElBQUlDLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7Z0JBQ25CQyxTQUFTO2dCQUNUbEIsT0FBTztZQUNUO1FBQ0Y7SUFDRjtBQUNGO0FBRUE7O0NBRUMsR0FDTSxTQUFTb0IseUJBQXlCM0IsUUFBZ0IsRUFBRUMsS0FBYSxFQUFFWCxRQUFnQjtJQUN4RixzQkFBc0I7SUFDdEIsSUFBSSxDQUFDVSxZQUFZQSxTQUFTNEIsTUFBTSxHQUFHLEtBQUs1QixTQUFTNEIsTUFBTSxHQUFHLElBQUk7UUFDNUQsT0FBTztJQUNUO0lBRUEsSUFBSSxDQUFDLGtCQUFrQkMsSUFBSSxDQUFDN0IsV0FBVztRQUNyQyxPQUFPO0lBQ1Q7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTThCLGFBQWE7SUFDbkIsSUFBSSxDQUFDN0IsU0FBUyxDQUFDNkIsV0FBV0QsSUFBSSxDQUFDNUIsUUFBUTtRQUNyQyxPQUFPO0lBQ1Q7SUFFQSxzQkFBc0I7SUFDdEIsSUFBSSxDQUFDWCxZQUFZQSxTQUFTc0MsTUFBTSxHQUFHLEdBQUc7UUFDcEMsT0FBTztJQUNUO0lBRUEsSUFBSSxDQUFDLGlDQUFpQ0MsSUFBSSxDQUFDdkMsV0FBVztRQUNwRCxPQUFPO0lBQ1Q7SUFFQSxPQUFPO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNNLFNBQVN5QyxrQkFBa0I5QixLQUFhLEVBQUVYLFFBQWdCO0lBQy9ELElBQUksQ0FBQ1csU0FBUyxDQUFDWCxVQUFVO1FBQ3ZCLE9BQU87SUFDVDtJQUVBLE1BQU13QyxhQUFhO0lBQ25CLElBQUksQ0FBQ0EsV0FBV0QsSUFBSSxDQUFDNUIsUUFBUTtRQUMzQixPQUFPO0lBQ1Q7SUFFQSxPQUFPO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNNLGVBQWUrQixhQUFhaEMsUUFBZ0IsRUFBRUMsS0FBYSxFQUFFWCxRQUFnQjtJQUNsRixJQUFJO1FBQ0Ysc0JBQXNCO1FBQ3RCLE1BQU0yQyxrQkFBa0JOLHlCQUF5QjNCLFVBQVVDLE9BQU9YO1FBQ2xFLElBQUkyQyxpQkFBaUI7WUFDbkIsT0FBTztnQkFBRVIsU0FBUztnQkFBT2xCLE9BQU8wQjtZQUFnQjtRQUNsRDtRQUVBLCtCQUErQjtRQUMvQixNQUFNQyxzQkFBc0JsRCw2Q0FBTUEsQ0FBQ21ELFdBQVcsQ0FBQ2xDO1FBQy9DLElBQUlpQyxxQkFBcUI7WUFDdkIsT0FBTztnQkFBRVQsU0FBUztnQkFBT2xCLE9BQU87WUFBMkI7UUFDN0Q7UUFFQSxNQUFNNkIseUJBQXlCcEQsNkNBQU1BLENBQUNxRCxjQUFjLENBQUNyQztRQUNyRCxJQUFJb0Msd0JBQXdCO1lBQzFCLE9BQU87Z0JBQUVYLFNBQVM7Z0JBQU9sQixPQUFPO1lBQXlCO1FBQzNEO1FBRUEsZ0JBQWdCO1FBQ2hCLE1BQU0rQixlQUFlLE1BQU1qRCxhQUFhQztRQUV4QyxjQUFjO1FBQ2QsTUFBTU0sT0FBT1osNkNBQU1BLENBQUN1RCxNQUFNLENBQUN2QyxVQUFVQyxPQUFPcUM7UUFFNUMscUNBQXFDO1FBQ3JDLE1BQU0sRUFBRUUsYUFBYSxFQUFFLEdBQUdDLHFCQUFxQixHQUFHN0M7UUFFbEQsT0FBTztZQUFFNkIsU0FBUztZQUFNN0IsTUFBTTZDO1FBQTRCO0lBQzVELEVBQUUsT0FBT2xDLE9BQU87UUFDZG1CLFFBQVFuQixLQUFLLENBQUMsdUJBQXVCQTtRQUNyQyxPQUFPO1lBQUVrQixTQUFTO1lBQU9sQixPQUFPO1FBQTBCO0lBQzVEO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWVtQyxVQUFVekMsS0FBYSxFQUFFWCxRQUFnQjtJQUM3RCxJQUFJO1FBQ0Ysc0JBQXNCO1FBQ3RCLE1BQU0yQyxrQkFBa0JGLGtCQUFrQjlCLE9BQU9YO1FBQ2pELElBQUkyQyxpQkFBaUI7WUFDbkIsT0FBTztnQkFBRVIsU0FBUztnQkFBT2xCLE9BQU8wQjtZQUFnQjtRQUNsRDtRQUVBLHFCQUFxQjtRQUNyQixNQUFNckMsT0FBT1osNkNBQU1BLENBQUNtRCxXQUFXLENBQUNsQztRQUNoQyxJQUFJLENBQUNMLE1BQU07WUFDVCxPQUFPO2dCQUFFNkIsU0FBUztnQkFBT2xCLE9BQU87WUFBNEI7UUFDOUQ7UUFFQSxrQkFBa0I7UUFDbEIsTUFBTW9DLGtCQUFrQixNQUFNbEQsZUFBZUgsVUFBVU0sS0FBSzRDLGFBQWE7UUFDekUsSUFBSSxDQUFDRyxpQkFBaUI7WUFDcEIsT0FBTztnQkFBRWxCLFNBQVM7Z0JBQU9sQixPQUFPO1lBQTRCO1FBQzlEO1FBRUEsaUJBQWlCO1FBQ2pCLE1BQU1GLFFBQVFWLGNBQWNDO1FBRTVCLHFDQUFxQztRQUNyQyxNQUFNLEVBQUU0QyxhQUFhLEVBQUUsR0FBR0MscUJBQXFCLEdBQUc3QztRQUVsRCxPQUFPO1lBQ0w2QixTQUFTO1lBQ1Q3QixNQUFNNkM7WUFDTnBDO1FBQ0Y7SUFDRixFQUFFLE9BQU9FLE9BQU87UUFDZG1CLFFBQVFuQixLQUFLLENBQUMsZ0JBQWdCQTtRQUM5QixPQUFPO1lBQUVrQixTQUFTO1lBQU9sQixPQUFPO1FBQWtCO0lBQ3BEO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLFNBQVNxQyxjQUFjdEIsR0FBb0IsRUFBRWpCLEtBQWE7SUFDL0QsTUFBTXdDLGVBQWUzRCxrQkFBeUI7SUFFOUNvQyxJQUFJd0IsU0FBUyxDQUFDLGNBQWM7UUFDMUIsQ0FBQyxNQUFNLEVBQUV6QyxNQUFNLDRCQUE0QixFQUFFLElBQUksS0FBSyxLQUFLLEdBQUcsaUJBQWlCLEVBQUV3QyxlQUFlLGFBQWEsSUFBSTtLQUNsSDtBQUNIO0FBRUE7O0NBRUMsR0FDTSxTQUFTRSxnQkFBZ0J6QixHQUFvQjtJQUNsREEsSUFBSXdCLFNBQVMsQ0FBQyxjQUFjO1FBQzFCO0tBQ0Q7QUFDSDtBQUVBOztDQUVDLEdBQ0QsTUFBTUUsZUFBZSxJQUFJQztBQUVsQixTQUFTQyxlQUFlQyxFQUFVLEVBQUVDLGNBQXNCLENBQUMsRUFBRUMsV0FBbUIsS0FBSyxLQUFLLElBQUk7SUFDbkcsTUFBTUMsTUFBTUMsS0FBS0QsR0FBRztJQUNwQixNQUFNRSxTQUFTUixhQUFhUyxHQUFHLENBQUNOO0lBRWhDLElBQUksQ0FBQ0ssVUFBVUYsTUFBTUUsT0FBT0UsU0FBUyxFQUFFO1FBQ3JDVixhQUFhVyxHQUFHLENBQUNSLElBQUk7WUFBRVMsT0FBTztZQUFHRixXQUFXSixNQUFNRDtRQUFTO1FBQzNELE9BQU87SUFDVDtJQUVBLElBQUlHLE9BQU9JLEtBQUssSUFBSVIsYUFBYTtRQUMvQixPQUFPO0lBQ1Q7SUFFQUksT0FBT0ksS0FBSztJQUNaLE9BQU87QUFDVDtBQUVBOztDQUVDLEdBQ00sU0FBU0MsWUFBWXBELEdBQW1CO0lBQzdDLE1BQU1xRCxZQUFZckQsSUFBSUUsT0FBTyxDQUFDLGtCQUFrQjtJQUNoRCxNQUFNd0MsS0FBS1csWUFBYUMsTUFBTUMsT0FBTyxDQUFDRixhQUFhQSxTQUFTLENBQUMsRUFBRSxHQUFHQSxVQUFVRyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsR0FBSXhELElBQUl5RCxNQUFNLENBQUNDLGFBQWE7SUFDckgsT0FBT2hCLE1BQU07QUFDZiIsInNvdXJjZXMiOlsiRTpcXDExMVxcUFJPSkVDVFxcbWluZXMtZ2FtZVxcbGliXFxhdXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBqd3QgZnJvbSAnanNvbndlYnRva2VuJztcbmltcG9ydCBiY3J5cHQgZnJvbSAnYmNyeXB0anMnO1xuaW1wb3J0IHsgTmV4dEFwaVJlcXVlc3QsIE5leHRBcGlSZXNwb25zZSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IHsgVXNlciB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgdXNlckRiIH0gZnJvbSAnLi9kYXRhYmFzZSc7XG5cbi8vIEpXVCBzZWNyZXQgLSBpbiBwcm9kdWN0aW9uLCB0aGlzIHNob3VsZCBiZSBpbiBlbnZpcm9ubWVudCB2YXJpYWJsZXNcbmNvbnN0IEpXVF9TRUNSRVQgPSBwcm9jZXNzLmVudi5KV1RfU0VDUkVUIHx8ICd5b3VyLXN1cGVyLXNlY3JldC1qd3Qta2V5LWNoYW5nZS10aGlzLWluLXByb2R1Y3Rpb24nO1xuY29uc3QgSldUX0VYUElSRVNfSU4gPSAnN2QnO1xuXG4vKipcbiAqIEhhc2ggcGFzc3dvcmQgdXNpbmcgYmNyeXB0XG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBoYXNoUGFzc3dvcmQocGFzc3dvcmQ6IHN0cmluZyk6IFByb21pc2U8c3RyaW5nPiB7XG4gIGNvbnN0IHNhbHRSb3VuZHMgPSAxMjtcbiAgcmV0dXJuIGJjcnlwdC5oYXNoKHBhc3N3b3JkLCBzYWx0Um91bmRzKTtcbn1cblxuLyoqXG4gKiBWZXJpZnkgcGFzc3dvcmQgYWdhaW5zdCBoYXNoXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB2ZXJpZnlQYXNzd29yZChwYXNzd29yZDogc3RyaW5nLCBoYXNoOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgcmV0dXJuIGJjcnlwdC5jb21wYXJlKHBhc3N3b3JkLCBoYXNoKTtcbn1cblxuLyoqXG4gKiBHZW5lcmF0ZSBKV1QgdG9rZW4gZm9yIHVzZXJcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlVG9rZW4odXNlcjogVXNlcik6IHN0cmluZyB7XG4gIGNvbnN0IHBheWxvYWQgPSB7XG4gICAgdXNlcklkOiB1c2VyLmlkLFxuICAgIHVzZXJuYW1lOiB1c2VyLnVzZXJuYW1lLFxuICAgIGVtYWlsOiB1c2VyLmVtYWlsXG4gIH07XG4gIFxuICByZXR1cm4gand0LnNpZ24ocGF5bG9hZCwgSldUX1NFQ1JFVCwgeyBleHBpcmVzSW46IEpXVF9FWFBJUkVTX0lOIH0pO1xufVxuXG4vKipcbiAqIFZlcmlmeSBKV1QgdG9rZW4gYW5kIHJldHVybiB1c2VyIGRhdGFcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHZlcmlmeVRva2VuKHRva2VuOiBzdHJpbmcpOiBhbnkge1xuICB0cnkge1xuICAgIHJldHVybiBqd3QudmVyaWZ5KHRva2VuLCBKV1RfU0VDUkVUKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxufVxuXG4vKipcbiAqIEV4dHJhY3QgdG9rZW4gZnJvbSByZXF1ZXN0IGhlYWRlcnNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGV4dHJhY3RUb2tlbkZyb21SZXF1ZXN0KHJlcTogTmV4dEFwaVJlcXVlc3QpOiBzdHJpbmcgfCBudWxsIHtcbiAgY29uc3QgYXV0aEhlYWRlciA9IHJlcS5oZWFkZXJzLmF1dGhvcml6YXRpb247XG4gIFxuICBpZiAoYXV0aEhlYWRlciAmJiBhdXRoSGVhZGVyLnN0YXJ0c1dpdGgoJ0JlYXJlciAnKSkge1xuICAgIHJldHVybiBhdXRoSGVhZGVyLnN1YnN0cmluZyg3KTtcbiAgfVxuICBcbiAgLy8gQWxzbyBjaGVjayBjb29raWVzIGZvciB0b2tlblxuICBjb25zdCBjb29raWVUb2tlbiA9IHJlcS5jb29raWVzLnRva2VuO1xuICBpZiAoY29va2llVG9rZW4pIHtcbiAgICByZXR1cm4gY29va2llVG9rZW47XG4gIH1cbiAgXG4gIHJldHVybiBudWxsO1xufVxuXG4vKipcbiAqIE1pZGRsZXdhcmUgdG8gYXV0aGVudGljYXRlIHJlcXVlc3RzXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBhdXRoZW50aWNhdGVSZXF1ZXN0KHJlcTogTmV4dEFwaVJlcXVlc3QpOiBVc2VyIHwgbnVsbCB7XG4gIGNvbnN0IHRva2VuID0gZXh0cmFjdFRva2VuRnJvbVJlcXVlc3QocmVxKTtcbiAgXG4gIGlmICghdG9rZW4pIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuICBcbiAgY29uc3QgZGVjb2RlZCA9IHZlcmlmeVRva2VuKHRva2VuKTtcbiAgaWYgKCFkZWNvZGVkIHx8ICFkZWNvZGVkLnVzZXJJZCkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG4gIFxuICAvLyBHZXQgZnJlc2ggdXNlciBkYXRhIGZyb20gZGF0YWJhc2VcbiAgY29uc3QgdXNlciA9IHVzZXJEYi5maW5kQnlJZChkZWNvZGVkLnVzZXJJZCk7XG4gIHJldHVybiB1c2VyO1xufVxuXG4vKipcbiAqIEhpZ2hlci1vcmRlciBmdW5jdGlvbiB0byBwcm90ZWN0IEFQSSByb3V0ZXNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdpdGhBdXRoKGhhbmRsZXI6IChyZXE6IE5leHRBcGlSZXF1ZXN0LCByZXM6IE5leHRBcGlSZXNwb25zZSwgdXNlcjogVXNlcikgPT4gUHJvbWlzZTx2b2lkPikge1xuICByZXR1cm4gYXN5bmMgKHJlcTogTmV4dEFwaVJlcXVlc3QsIHJlczogTmV4dEFwaVJlc3BvbnNlKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXIgPSBhdXRoZW50aWNhdGVSZXF1ZXN0KHJlcSk7XG4gICAgICBcbiAgICAgIGlmICghdXNlcikge1xuICAgICAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDEpLmpzb24oe1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiAnQXV0aGVudGljYXRpb24gcmVxdWlyZWQnXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgICAgXG4gICAgICBhd2FpdCBoYW5kbGVyKHJlcSwgcmVzLCB1c2VyKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignQXV0aCBtaWRkbGV3YXJlIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHJlcy5zdGF0dXMoNTAwKS5qc29uKHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yJ1xuICAgICAgfSk7XG4gICAgfVxuICB9O1xufVxuXG4vKipcbiAqIFZhbGlkYXRlIHVzZXIgcmVnaXN0cmF0aW9uIGRhdGFcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHZhbGlkYXRlUmVnaXN0cmF0aW9uRGF0YSh1c2VybmFtZTogc3RyaW5nLCBlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKTogc3RyaW5nIHwgbnVsbCB7XG4gIC8vIFVzZXJuYW1lIHZhbGlkYXRpb25cbiAgaWYgKCF1c2VybmFtZSB8fCB1c2VybmFtZS5sZW5ndGggPCAzIHx8IHVzZXJuYW1lLmxlbmd0aCA+IDIwKSB7XG4gICAgcmV0dXJuICdVc2VybmFtZSBtdXN0IGJlIGJldHdlZW4gMyBhbmQgMjAgY2hhcmFjdGVycyc7XG4gIH1cbiAgXG4gIGlmICghL15bYS16QS1aMC05X10rJC8udGVzdCh1c2VybmFtZSkpIHtcbiAgICByZXR1cm4gJ1VzZXJuYW1lIGNhbiBvbmx5IGNvbnRhaW4gbGV0dGVycywgbnVtYmVycywgYW5kIHVuZGVyc2NvcmVzJztcbiAgfVxuICBcbiAgLy8gRW1haWwgdmFsaWRhdGlvblxuICBjb25zdCBlbWFpbFJlZ2V4ID0gL15bXlxcc0BdK0BbXlxcc0BdK1xcLlteXFxzQF0rJC87XG4gIGlmICghZW1haWwgfHwgIWVtYWlsUmVnZXgudGVzdChlbWFpbCkpIHtcbiAgICByZXR1cm4gJ1BsZWFzZSBwcm92aWRlIGEgdmFsaWQgZW1haWwgYWRkcmVzcyc7XG4gIH1cbiAgXG4gIC8vIFBhc3N3b3JkIHZhbGlkYXRpb25cbiAgaWYgKCFwYXNzd29yZCB8fCBwYXNzd29yZC5sZW5ndGggPCA4KSB7XG4gICAgcmV0dXJuICdQYXNzd29yZCBtdXN0IGJlIGF0IGxlYXN0IDggY2hhcmFjdGVycyBsb25nJztcbiAgfVxuICBcbiAgaWYgKCEvKD89LipbYS16XSkoPz0uKltBLVpdKSg/PS4qXFxkKS8udGVzdChwYXNzd29yZCkpIHtcbiAgICByZXR1cm4gJ1Bhc3N3b3JkIG11c3QgY29udGFpbiBhdCBsZWFzdCBvbmUgdXBwZXJjYXNlIGxldHRlciwgb25lIGxvd2VyY2FzZSBsZXR0ZXIsIGFuZCBvbmUgbnVtYmVyJztcbiAgfVxuICBcbiAgcmV0dXJuIG51bGw7XG59XG5cbi8qKlxuICogVmFsaWRhdGUgbG9naW4gZGF0YVxuICovXG5leHBvcnQgZnVuY3Rpb24gdmFsaWRhdGVMb2dpbkRhdGEoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZyk6IHN0cmluZyB8IG51bGwge1xuICBpZiAoIWVtYWlsIHx8ICFwYXNzd29yZCkge1xuICAgIHJldHVybiAnRW1haWwgYW5kIHBhc3N3b3JkIGFyZSByZXF1aXJlZCc7XG4gIH1cbiAgXG4gIGNvbnN0IGVtYWlsUmVnZXggPSAvXlteXFxzQF0rQFteXFxzQF0rXFwuW15cXHNAXSskLztcbiAgaWYgKCFlbWFpbFJlZ2V4LnRlc3QoZW1haWwpKSB7XG4gICAgcmV0dXJuICdQbGVhc2UgcHJvdmlkZSBhIHZhbGlkIGVtYWlsIGFkZHJlc3MnO1xuICB9XG4gIFxuICByZXR1cm4gbnVsbDtcbn1cblxuLyoqXG4gKiBSZWdpc3RlciBuZXcgdXNlclxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmVnaXN0ZXJVc2VyKHVzZXJuYW1lOiBzdHJpbmcsIGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgdXNlcj86IFVzZXI7IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgdHJ5IHtcbiAgICAvLyBWYWxpZGF0ZSBpbnB1dCBkYXRhXG4gICAgY29uc3QgdmFsaWRhdGlvbkVycm9yID0gdmFsaWRhdGVSZWdpc3RyYXRpb25EYXRhKHVzZXJuYW1lLCBlbWFpbCwgcGFzc3dvcmQpO1xuICAgIGlmICh2YWxpZGF0aW9uRXJyb3IpIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogdmFsaWRhdGlvbkVycm9yIH07XG4gICAgfVxuICAgIFxuICAgIC8vIENoZWNrIGlmIHVzZXIgYWxyZWFkeSBleGlzdHNcbiAgICBjb25zdCBleGlzdGluZ1VzZXJCeUVtYWlsID0gdXNlckRiLmZpbmRCeUVtYWlsKGVtYWlsKTtcbiAgICBpZiAoZXhpc3RpbmdVc2VyQnlFbWFpbCkge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnRW1haWwgYWxyZWFkeSByZWdpc3RlcmVkJyB9O1xuICAgIH1cbiAgICBcbiAgICBjb25zdCBleGlzdGluZ1VzZXJCeVVzZXJuYW1lID0gdXNlckRiLmZpbmRCeVVzZXJuYW1lKHVzZXJuYW1lKTtcbiAgICBpZiAoZXhpc3RpbmdVc2VyQnlVc2VybmFtZSkge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnVXNlcm5hbWUgYWxyZWFkeSB0YWtlbicgfTtcbiAgICB9XG4gICAgXG4gICAgLy8gSGFzaCBwYXNzd29yZFxuICAgIGNvbnN0IHBhc3N3b3JkSGFzaCA9IGF3YWl0IGhhc2hQYXNzd29yZChwYXNzd29yZCk7XG4gICAgXG4gICAgLy8gQ3JlYXRlIHVzZXJcbiAgICBjb25zdCB1c2VyID0gdXNlckRiLmNyZWF0ZSh1c2VybmFtZSwgZW1haWwsIHBhc3N3b3JkSGFzaCk7XG4gICAgXG4gICAgLy8gUmVtb3ZlIHBhc3N3b3JkIGhhc2ggZnJvbSByZXNwb25zZVxuICAgIGNvbnN0IHsgcGFzc3dvcmRfaGFzaCwgLi4udXNlcldpdGhvdXRQYXNzd29yZCB9ID0gdXNlcjtcbiAgICBcbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCB1c2VyOiB1c2VyV2l0aG91dFBhc3N3b3JkIGFzIFVzZXIgfTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdSZWdpc3RyYXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ZhaWxlZCB0byByZWdpc3RlciB1c2VyJyB9O1xuICB9XG59XG5cbi8qKlxuICogTG9naW4gdXNlclxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbG9naW5Vc2VyKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgdXNlcj86IFVzZXI7IHRva2VuPzogc3RyaW5nOyBlcnJvcj86IHN0cmluZyB9PiB7XG4gIHRyeSB7XG4gICAgLy8gVmFsaWRhdGUgaW5wdXQgZGF0YVxuICAgIGNvbnN0IHZhbGlkYXRpb25FcnJvciA9IHZhbGlkYXRlTG9naW5EYXRhKGVtYWlsLCBwYXNzd29yZCk7XG4gICAgaWYgKHZhbGlkYXRpb25FcnJvcikge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiB2YWxpZGF0aW9uRXJyb3IgfTtcbiAgICB9XG4gICAgXG4gICAgLy8gRmluZCB1c2VyIGJ5IGVtYWlsXG4gICAgY29uc3QgdXNlciA9IHVzZXJEYi5maW5kQnlFbWFpbChlbWFpbCk7XG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdJbnZhbGlkIGVtYWlsIG9yIHBhc3N3b3JkJyB9O1xuICAgIH1cbiAgICBcbiAgICAvLyBWZXJpZnkgcGFzc3dvcmRcbiAgICBjb25zdCBpc1ZhbGlkUGFzc3dvcmQgPSBhd2FpdCB2ZXJpZnlQYXNzd29yZChwYXNzd29yZCwgdXNlci5wYXNzd29yZF9oYXNoKTtcbiAgICBpZiAoIWlzVmFsaWRQYXNzd29yZCkge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnSW52YWxpZCBlbWFpbCBvciBwYXNzd29yZCcgfTtcbiAgICB9XG4gICAgXG4gICAgLy8gR2VuZXJhdGUgdG9rZW5cbiAgICBjb25zdCB0b2tlbiA9IGdlbmVyYXRlVG9rZW4odXNlcik7XG4gICAgXG4gICAgLy8gUmVtb3ZlIHBhc3N3b3JkIGhhc2ggZnJvbSByZXNwb25zZVxuICAgIGNvbnN0IHsgcGFzc3dvcmRfaGFzaCwgLi4udXNlcldpdGhvdXRQYXNzd29yZCB9ID0gdXNlcjtcbiAgICBcbiAgICByZXR1cm4geyBcbiAgICAgIHN1Y2Nlc3M6IHRydWUsIFxuICAgICAgdXNlcjogdXNlcldpdGhvdXRQYXNzd29yZCBhcyBVc2VyLCBcbiAgICAgIHRva2VuIFxuICAgIH07XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignTG9naW4gZXJyb3I6JywgZXJyb3IpO1xuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ZhaWxlZCB0byBsb2dpbicgfTtcbiAgfVxufVxuXG4vKipcbiAqIFNldCBhdXRoZW50aWNhdGlvbiBjb29raWVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHNldEF1dGhDb29raWUocmVzOiBOZXh0QXBpUmVzcG9uc2UsIHRva2VuOiBzdHJpbmcpOiB2b2lkIHtcbiAgY29uc3QgaXNQcm9kdWN0aW9uID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJztcbiAgXG4gIHJlcy5zZXRIZWFkZXIoJ1NldC1Db29raWUnLCBbXG4gICAgYHRva2VuPSR7dG9rZW59OyBIdHRwT25seTsgUGF0aD0vOyBNYXgtQWdlPSR7NyAqIDI0ICogNjAgKiA2MH07IFNhbWVTaXRlPVN0cmljdCR7aXNQcm9kdWN0aW9uID8gJzsgU2VjdXJlJyA6ICcnfWBcbiAgXSk7XG59XG5cbi8qKlxuICogQ2xlYXIgYXV0aGVudGljYXRpb24gY29va2llXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjbGVhckF1dGhDb29raWUocmVzOiBOZXh0QXBpUmVzcG9uc2UpOiB2b2lkIHtcbiAgcmVzLnNldEhlYWRlcignU2V0LUNvb2tpZScsIFtcbiAgICAndG9rZW49OyBIdHRwT25seTsgUGF0aD0vOyBNYXgtQWdlPTA7IFNhbWVTaXRlPVN0cmljdCdcbiAgXSk7XG59XG5cbi8qKlxuICogUmF0ZSBsaW1pdGluZyBmb3IgYXV0aGVudGljYXRpb24gZW5kcG9pbnRzXG4gKi9cbmNvbnN0IHJhdGVMaW1pdE1hcCA9IG5ldyBNYXA8c3RyaW5nLCB7IGNvdW50OiBudW1iZXI7IHJlc2V0VGltZTogbnVtYmVyIH0+KCk7XG5cbmV4cG9ydCBmdW5jdGlvbiBjaGVja1JhdGVMaW1pdChpcDogc3RyaW5nLCBtYXhBdHRlbXB0czogbnVtYmVyID0gNSwgd2luZG93TXM6IG51bWJlciA9IDE1ICogNjAgKiAxMDAwKTogYm9vbGVhbiB7XG4gIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gIGNvbnN0IHJlY29yZCA9IHJhdGVMaW1pdE1hcC5nZXQoaXApO1xuICBcbiAgaWYgKCFyZWNvcmQgfHwgbm93ID4gcmVjb3JkLnJlc2V0VGltZSkge1xuICAgIHJhdGVMaW1pdE1hcC5zZXQoaXAsIHsgY291bnQ6IDEsIHJlc2V0VGltZTogbm93ICsgd2luZG93TXMgfSk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cbiAgXG4gIGlmIChyZWNvcmQuY291bnQgPj0gbWF4QXR0ZW1wdHMpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgXG4gIHJlY29yZC5jb3VudCsrO1xuICByZXR1cm4gdHJ1ZTtcbn1cblxuLyoqXG4gKiBHZXQgY2xpZW50IElQIGFkZHJlc3NcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldENsaWVudElQKHJlcTogTmV4dEFwaVJlcXVlc3QpOiBzdHJpbmcge1xuICBjb25zdCBmb3J3YXJkZWQgPSByZXEuaGVhZGVyc1sneC1mb3J3YXJkZWQtZm9yJ107XG4gIGNvbnN0IGlwID0gZm9yd2FyZGVkID8gKEFycmF5LmlzQXJyYXkoZm9yd2FyZGVkKSA/IGZvcndhcmRlZFswXSA6IGZvcndhcmRlZC5zcGxpdCgnLCcpWzBdKSA6IHJlcS5zb2NrZXQucmVtb3RlQWRkcmVzcztcbiAgcmV0dXJuIGlwIHx8ICd1bmtub3duJztcbn1cbiJdLCJuYW1lcyI6WyJqd3QiLCJiY3J5cHQiLCJ1c2VyRGIiLCJKV1RfU0VDUkVUIiwicHJvY2VzcyIsImVudiIsIkpXVF9FWFBJUkVTX0lOIiwiaGFzaFBhc3N3b3JkIiwicGFzc3dvcmQiLCJzYWx0Um91bmRzIiwiaGFzaCIsInZlcmlmeVBhc3N3b3JkIiwiY29tcGFyZSIsImdlbmVyYXRlVG9rZW4iLCJ1c2VyIiwicGF5bG9hZCIsInVzZXJJZCIsImlkIiwidXNlcm5hbWUiLCJlbWFpbCIsInNpZ24iLCJleHBpcmVzSW4iLCJ2ZXJpZnlUb2tlbiIsInRva2VuIiwidmVyaWZ5IiwiZXJyb3IiLCJleHRyYWN0VG9rZW5Gcm9tUmVxdWVzdCIsInJlcSIsImF1dGhIZWFkZXIiLCJoZWFkZXJzIiwiYXV0aG9yaXphdGlvbiIsInN0YXJ0c1dpdGgiLCJzdWJzdHJpbmciLCJjb29raWVUb2tlbiIsImNvb2tpZXMiLCJhdXRoZW50aWNhdGVSZXF1ZXN0IiwiZGVjb2RlZCIsImZpbmRCeUlkIiwid2l0aEF1dGgiLCJoYW5kbGVyIiwicmVzIiwic3RhdHVzIiwianNvbiIsInN1Y2Nlc3MiLCJjb25zb2xlIiwidmFsaWRhdGVSZWdpc3RyYXRpb25EYXRhIiwibGVuZ3RoIiwidGVzdCIsImVtYWlsUmVnZXgiLCJ2YWxpZGF0ZUxvZ2luRGF0YSIsInJlZ2lzdGVyVXNlciIsInZhbGlkYXRpb25FcnJvciIsImV4aXN0aW5nVXNlckJ5RW1haWwiLCJmaW5kQnlFbWFpbCIsImV4aXN0aW5nVXNlckJ5VXNlcm5hbWUiLCJmaW5kQnlVc2VybmFtZSIsInBhc3N3b3JkSGFzaCIsImNyZWF0ZSIsInBhc3N3b3JkX2hhc2giLCJ1c2VyV2l0aG91dFBhc3N3b3JkIiwibG9naW5Vc2VyIiwiaXNWYWxpZFBhc3N3b3JkIiwic2V0QXV0aENvb2tpZSIsImlzUHJvZHVjdGlvbiIsInNldEhlYWRlciIsImNsZWFyQXV0aENvb2tpZSIsInJhdGVMaW1pdE1hcCIsIk1hcCIsImNoZWNrUmF0ZUxpbWl0IiwiaXAiLCJtYXhBdHRlbXB0cyIsIndpbmRvd01zIiwibm93IiwiRGF0ZSIsInJlY29yZCIsImdldCIsInJlc2V0VGltZSIsInNldCIsImNvdW50IiwiZ2V0Q2xpZW50SVAiLCJmb3J3YXJkZWQiLCJBcnJheSIsImlzQXJyYXkiLCJzcGxpdCIsInNvY2tldCIsInJlbW90ZUFkZHJlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api-node)/./lib/auth.ts\n");

/***/ }),

/***/ "(api-node)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   gameDb: () => (/* binding */ gameDb),\n/* harmony export */   gameSessionDb: () => (/* binding */ gameSessionDb),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   initDatabase: () => (/* binding */ initDatabase),\n/* harmony export */   leaderboardDb: () => (/* binding */ leaderboardDb),\n/* harmony export */   transactionDb: () => (/* binding */ transactionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb),\n/* harmony export */   userStatsDb: () => (/* binding */ userStatsDb)\n/* harmony export */ });\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(better_sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Database instance\nlet db = null;\n/**\n * Initialize database connection and create tables\n */ function initDatabase() {\n    if (db) return db;\n    const dbPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data', 'mines.db');\n    try {\n        // Ensure data directory exists\n        const fs = __webpack_require__(/*! fs */ \"fs\");\n        const dataDir = path__WEBPACK_IMPORTED_MODULE_1___default().dirname(dbPath);\n        if (!fs.existsSync(dataDir)) {\n            fs.mkdirSync(dataDir, {\n                recursive: true\n            });\n        }\n        db = new (better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default())(dbPath);\n        // Enable WAL mode for better performance\n        db.pragma('journal_mode = WAL');\n        db.pragma('synchronous = NORMAL');\n        db.pragma('cache_size = 1000000');\n        db.pragma('temp_store = memory');\n        createTables();\n        console.log('Database initialized successfully');\n        return db;\n    } catch (error) {\n        console.error('Failed to initialize database:', error);\n        throw error;\n    }\n}\n/**\n * Create database tables\n */ function createTables() {\n    if (!db) throw new Error('Database not initialized');\n    // Users table\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS users (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      username TEXT UNIQUE NOT NULL,\n      email TEXT UNIQUE NOT NULL,\n      password_hash TEXT NOT NULL,\n      usdt_balance REAL DEFAULT 0.0,\n      ltc_balance REAL DEFAULT 0.0,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n    // Check if games table exists and what schema it has\n    const tableExists = db.prepare(\"SELECT name FROM sqlite_master WHERE type='table' AND name='games'\").get();\n    if (tableExists) {\n        // Check if we need to migrate old games table\n        const tableInfo = db.prepare(\"PRAGMA table_info(games)\").all();\n        const hasOldColumns = tableInfo.some((col)=>[\n                'grid_size',\n                'mine_count',\n                'revealed_cells',\n                'mine_positions'\n            ].includes(col.name));\n        const hasGameType = tableInfo.some((col)=>col.name === 'game_type');\n        const hasGameData = tableInfo.some((col)=>col.name === 'game_data');\n        if (hasOldColumns || !hasGameType || !hasGameData) {\n            console.log('🔄 Migrating games table to support multiple game types...');\n            migrateGamesTable();\n        }\n    } else {\n        // Create new table with correct schema\n        console.log('📋 Creating new games table with multi-game support...');\n        db.exec(`\n      CREATE TABLE games (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        game_type TEXT NOT NULL DEFAULT 'mines',\n        bet_amount REAL NOT NULL,\n        current_multiplier REAL DEFAULT 1.0,\n        status TEXT CHECK(status IN ('active', 'won', 'lost', 'cashed_out', 'cancelled')) DEFAULT 'active',\n        server_seed TEXT NOT NULL,\n        client_seed TEXT NOT NULL,\n        server_seed_hash TEXT DEFAULT '',\n        profit REAL DEFAULT 0.0,\n        game_data TEXT DEFAULT '{}',\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n    }\n    // Transactions table\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS transactions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      game_id INTEGER,\n      type TEXT CHECK(type IN ('deposit', 'withdraw', 'bet', 'win')) NOT NULL,\n      currency TEXT CHECK(currency IN ('USDT', 'LTC')) NOT NULL,\n      amount REAL NOT NULL,\n      status TEXT CHECK(status IN ('pending', 'completed', 'failed')) DEFAULT 'pending',\n      transaction_hash TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (user_id) REFERENCES users (id),\n      FOREIGN KEY (game_id) REFERENCES games (id)\n    )\n  `);\n    // Phase 2: Enhanced tables for leaderboards, statistics, and sessions\n    // User Statistics table - tracks detailed user performance\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS user_statistics (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      game_type TEXT NOT NULL,\n      total_games INTEGER DEFAULT 0,\n      total_wins INTEGER DEFAULT 0,\n      total_losses INTEGER DEFAULT 0,\n      total_wagered REAL DEFAULT 0.0,\n      total_profit REAL DEFAULT 0.0,\n      biggest_win REAL DEFAULT 0.0,\n      biggest_loss REAL DEFAULT 0.0,\n      highest_multiplier REAL DEFAULT 0.0,\n      current_streak INTEGER DEFAULT 0,\n      best_win_streak INTEGER DEFAULT 0,\n      best_loss_streak INTEGER DEFAULT 0,\n      last_played DATETIME,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (user_id) REFERENCES users (id),\n      UNIQUE(user_id, game_type)\n    )\n  `);\n    // Leaderboards table - tracks top performers\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS leaderboards (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      username TEXT NOT NULL,\n      game_type TEXT NOT NULL,\n      category TEXT NOT NULL, -- 'profit', 'multiplier', 'streak', 'volume'\n      value REAL NOT NULL,\n      rank_position INTEGER,\n      period TEXT NOT NULL, -- 'daily', 'weekly', 'monthly', 'all_time'\n      period_start DATETIME NOT NULL,\n      period_end DATETIME NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (user_id) REFERENCES users (id),\n      UNIQUE(user_id, game_type, category, period, period_start)\n    )\n  `);\n    // Game Sessions table - tracks user gaming sessions\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS game_sessions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      session_start DATETIME DEFAULT CURRENT_TIMESTAMP,\n      session_end DATETIME,\n      total_games INTEGER DEFAULT 0,\n      total_wagered REAL DEFAULT 0.0,\n      total_profit REAL DEFAULT 0.0,\n      games_won INTEGER DEFAULT 0,\n      games_lost INTEGER DEFAULT 0,\n      biggest_win REAL DEFAULT 0.0,\n      biggest_loss REAL DEFAULT 0.0,\n      is_active BOOLEAN DEFAULT 1,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (user_id) REFERENCES users (id)\n    )\n  `);\n    // Achievements table - tracks user achievements and badges\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS achievements (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      code TEXT UNIQUE NOT NULL,\n      name TEXT NOT NULL,\n      description TEXT NOT NULL,\n      icon TEXT NOT NULL,\n      category TEXT NOT NULL, -- 'wins', 'profit', 'streak', 'volume', 'special'\n      requirement_type TEXT NOT NULL, -- 'count', 'value', 'streak'\n      requirement_value REAL NOT NULL,\n      reward_type TEXT, -- 'badge', 'bonus', 'title'\n      reward_value REAL DEFAULT 0.0,\n      is_active BOOLEAN DEFAULT 1,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n    // User Achievements table - tracks which achievements users have earned\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS user_achievements (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      achievement_id INTEGER NOT NULL,\n      earned_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      progress REAL DEFAULT 0.0,\n      is_completed BOOLEAN DEFAULT 0,\n      FOREIGN KEY (user_id) REFERENCES users (id),\n      FOREIGN KEY (achievement_id) REFERENCES achievements (id),\n      UNIQUE(user_id, achievement_id)\n    )\n  `);\n    // Create indexes for better performance\n    db.exec(`\n    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);\n    CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);\n    CREATE INDEX IF NOT EXISTS idx_games_user_id ON games(user_id);\n    CREATE INDEX IF NOT EXISTS idx_games_status ON games(status);\n    CREATE INDEX IF NOT EXISTS idx_games_type ON games(game_type);\n    CREATE INDEX IF NOT EXISTS idx_games_user_type ON games(user_id, game_type);\n    CREATE INDEX IF NOT EXISTS idx_games_user_status ON games(user_id, status);\n    CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);\n    CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);\n\n    -- Phase 2 indexes\n    CREATE INDEX IF NOT EXISTS idx_user_statistics_user_game ON user_statistics(user_id, game_type);\n    CREATE INDEX IF NOT EXISTS idx_leaderboards_game_category ON leaderboards(game_type, category);\n    CREATE INDEX IF NOT EXISTS idx_leaderboards_period ON leaderboards(period, period_start);\n    CREATE INDEX IF NOT EXISTS idx_leaderboards_rank ON leaderboards(rank_position);\n    CREATE INDEX IF NOT EXISTS idx_game_sessions_user_active ON game_sessions(user_id, is_active);\n    CREATE INDEX IF NOT EXISTS idx_game_sessions_start ON game_sessions(session_start);\n    CREATE INDEX IF NOT EXISTS idx_achievements_category ON achievements(category);\n    CREATE INDEX IF NOT EXISTS idx_user_achievements_user ON user_achievements(user_id);\n    CREATE INDEX IF NOT EXISTS idx_user_achievements_completed ON user_achievements(is_completed);\n  `);\n    // Create triggers for updated_at\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_users_timestamp\n    AFTER UPDATE ON users\n    BEGIN\n      UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_games_timestamp\n    AFTER UPDATE ON games\n    BEGIN\n      UPDATE games SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n    // Phase 2 triggers\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_user_statistics_timestamp\n    AFTER UPDATE ON user_statistics\n    BEGIN\n      UPDATE user_statistics SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_leaderboards_timestamp\n    AFTER UPDATE ON leaderboards\n    BEGIN\n      UPDATE leaderboards SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_game_sessions_timestamp\n    AFTER UPDATE ON game_sessions\n    BEGIN\n      UPDATE game_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n}\n/**\n * Migrate old games table to support multiple game types\n */ function migrateGamesTable() {\n    if (!db) throw new Error('Database not initialized');\n    try {\n        // Check if old columns exist\n        const tableInfo = db.prepare(\"PRAGMA table_info(games)\").all();\n        const hasOldColumns = tableInfo.some((col)=>[\n                'grid_size',\n                'mine_count',\n                'revealed_cells',\n                'mine_positions'\n            ].includes(col.name));\n        if (hasOldColumns) {\n            console.log('📦 Migrating existing mines games...');\n            // Disable foreign key constraints during migration\n            db.exec('PRAGMA foreign_keys = OFF');\n            // First, backup existing data\n            const existingGames = db.prepare(`\n        SELECT * FROM games\n      `).all();\n            console.log(`📋 Found ${existingGames.length} existing games to migrate`);\n            // Create new table with correct schema (without foreign key for now)\n            db.exec(`\n        CREATE TABLE games_new (\n          id INTEGER PRIMARY KEY AUTOINCREMENT,\n          user_id INTEGER NOT NULL,\n          game_type TEXT NOT NULL DEFAULT 'mines',\n          bet_amount REAL NOT NULL,\n          current_multiplier REAL DEFAULT 1.0,\n          status TEXT CHECK(status IN ('active', 'won', 'lost', 'cashed_out', 'cancelled')) DEFAULT 'active',\n          server_seed TEXT NOT NULL,\n          client_seed TEXT NOT NULL,\n          server_seed_hash TEXT DEFAULT '',\n          profit REAL DEFAULT 0.0,\n          game_data TEXT DEFAULT '{}',\n          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n        )\n      `);\n            // Migrate data to new table\n            for (const game of existingGames){\n                const gameData = {\n                    grid_size: game.grid_size || 25,\n                    mine_count: game.mine_count,\n                    revealed_cells: JSON.parse(game.revealed_cells || '[]'),\n                    mine_positions: JSON.parse(game.mine_positions || '[]')\n                };\n                db.prepare(`\n          INSERT INTO games_new (\n            id, user_id, game_type, bet_amount, current_multiplier,\n            status, server_seed, client_seed, server_seed_hash, profit,\n            game_data, created_at, updated_at\n          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n        `).run(game.id, game.user_id, 'mines', game.bet_amount, game.current_multiplier, game.status, game.server_seed, game.client_seed, game.server_seed_hash || '', game.profit, JSON.stringify(gameData), game.created_at, game.updated_at);\n            }\n            // Drop old table and rename new one\n            db.exec(`DROP TABLE games`);\n            db.exec(`ALTER TABLE games_new RENAME TO games`);\n            // Recreate indexes\n            db.exec(`\n        CREATE INDEX IF NOT EXISTS idx_games_user_id ON games(user_id);\n        CREATE INDEX IF NOT EXISTS idx_games_status ON games(status);\n        CREATE INDEX IF NOT EXISTS idx_games_type ON games(game_type);\n        CREATE INDEX IF NOT EXISTS idx_games_user_type ON games(user_id, game_type);\n        CREATE INDEX IF NOT EXISTS idx_games_user_status ON games(user_id, status);\n      `);\n            // Recreate trigger\n            db.exec(`\n        CREATE TRIGGER IF NOT EXISTS update_games_timestamp\n        AFTER UPDATE ON games\n        BEGIN\n          UPDATE games SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n        END;\n      `);\n            // Re-enable foreign key constraints\n            db.exec('PRAGMA foreign_keys = ON');\n            console.log(`✅ Migrated ${existingGames.length} mines games to new schema`);\n        }\n        console.log('✅ Games table migration completed');\n    } catch (error) {\n        console.error('❌ Games table migration failed:', error);\n        throw error;\n    }\n}\n/**\n * Get database instance\n */ function getDatabase() {\n    if (!db) {\n        return initDatabase();\n    }\n    return db;\n}\n/**\n * Close database connection\n */ function closeDatabase() {\n    if (db) {\n        db.close();\n        db = null;\n    }\n}\n/**\n * User database operations\n */ const userDb = {\n    create: (username, email, passwordHash)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      INSERT INTO users (username, email, password_hash)\n      VALUES (?, ?, ?)\n    `);\n        const result = stmt.run(username, email, passwordHash);\n        return userDb.findById(result.lastInsertRowid);\n    },\n    findById: (id)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM users WHERE id = ?');\n        return stmt.get(id);\n    },\n    findByEmail: (email)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM users WHERE email = ?');\n        return stmt.get(email);\n    },\n    findByUsername: (username)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM users WHERE username = ?');\n        return stmt.get(username);\n    },\n    updateBalance: (userId, currency, amount)=>{\n        const db = getDatabase();\n        const column = currency === 'USDT' ? 'usdt_balance' : 'ltc_balance';\n        const stmt = db.prepare(`UPDATE users SET ${column} = ? WHERE id = ?`);\n        const result = stmt.run(amount, userId);\n        return result.changes > 0;\n    },\n    addToBalance: (userId, currency, amount)=>{\n        const db = getDatabase();\n        const column = currency === 'USDT' ? 'usdt_balance' : 'ltc_balance';\n        const stmt = db.prepare(`UPDATE users SET ${column} = ${column} + ? WHERE id = ?`);\n        const result = stmt.run(amount, userId);\n        return result.changes > 0;\n    }\n};\n/**\n * Game database operations\n */ const gameDb = {\n    create: (gameData)=>{\n        const db = getDatabase();\n        // Extract game-specific data\n        const { game_type, user_id, bet_amount, current_multiplier, status, server_seed, client_seed, profit, ...specificData } = gameData;\n        const stmt = db.prepare(`\n      INSERT INTO games (\n        user_id, game_type, bet_amount, current_multiplier,\n        status, server_seed, client_seed, server_seed_hash, profit, game_data\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(user_id, game_type, bet_amount, current_multiplier, status, server_seed, client_seed, '', profit, JSON.stringify(specificData));\n        return gameDb.findById(result.lastInsertRowid);\n    },\n    findById: (id)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM games WHERE id = ?');\n        const game = stmt.get(id);\n        if (!game) return null;\n        return gameDb.parseGameData(game);\n    },\n    update: (id, updates)=>{\n        const db = getDatabase();\n        // Separate base fields from game-specific data\n        const { game_type, user_id, bet_amount, current_multiplier, status, server_seed, client_seed, profit, ...specificData } = updates;\n        const baseFields = {};\n        if (bet_amount !== undefined) baseFields.bet_amount = bet_amount;\n        if (current_multiplier !== undefined) baseFields.current_multiplier = current_multiplier;\n        if (status !== undefined) baseFields.status = status;\n        if (profit !== undefined) baseFields.profit = profit;\n        // If there's game-specific data, update game_data field\n        if (Object.keys(specificData).length > 0) {\n            // Get current game data and merge\n            const currentGame = gameDb.findById(id);\n            if (currentGame) {\n                const currentSpecificData = gameDb.extractGameSpecificData(currentGame);\n                const mergedData = {\n                    ...currentSpecificData,\n                    ...specificData\n                };\n                baseFields.game_data = JSON.stringify(mergedData);\n            }\n        }\n        if (Object.keys(baseFields).length === 0) return false;\n        const fields = Object.keys(baseFields);\n        const setClause = fields.map((field)=>`${field} = ?`).join(', ');\n        const values = fields.map((field)=>baseFields[field]);\n        const stmt = db.prepare(`UPDATE games SET ${setClause} WHERE id = ?`);\n        const result = stmt.run(...values, id);\n        return result.changes > 0;\n    },\n    findActiveByUserId: (userId)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM games WHERE user_id = ? AND status = ? ORDER BY created_at DESC LIMIT 1');\n        const game = stmt.get(userId, 'active');\n        if (!game) return null;\n        return gameDb.parseGameData(game);\n    },\n    findByUserId: (userId, limit = 50, gameType)=>{\n        const db = getDatabase();\n        let query = 'SELECT * FROM games WHERE user_id = ?';\n        const params = [\n            userId\n        ];\n        if (gameType) {\n            query += ' AND game_type = ?';\n            params.push(gameType);\n        }\n        query += ' ORDER BY created_at DESC LIMIT ?';\n        params.push(limit);\n        const stmt = db.prepare(query);\n        const games = stmt.all(...params);\n        return games.map((game)=>gameDb.parseGameData(game));\n    },\n    getActiveGamesByType: (gameType)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM games WHERE game_type = ? AND status = ? ORDER BY created_at DESC');\n        const games = stmt.all(gameType, 'active');\n        return games.map((game)=>gameDb.parseGameData(game));\n    },\n    // Helper method to parse game data from database\n    parseGameData: (dbGame)=>{\n        const gameData = JSON.parse(dbGame.game_data || '{}');\n        return {\n            ...dbGame,\n            ...gameData\n        };\n    },\n    // Helper method to extract game-specific data\n    extractGameSpecificData: (gameState)=>{\n        const { id, user_id, game_type, bet_amount, current_multiplier, status, server_seed, client_seed, profit, created_at, updated_at, ...specificData } = gameState;\n        return specificData;\n    }\n};\n/**\n * Transaction database operations\n */ const transactionDb = {\n    create: (transactionData)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      INSERT INTO transactions (user_id, game_id, type, currency, amount, status, transaction_hash)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(transactionData.user_id, transactionData.game_id || null, transactionData.type, transactionData.currency, transactionData.amount, transactionData.status, transactionData.transaction_hash || null);\n        return transactionDb.findById(result.lastInsertRowid);\n    },\n    findById: (id)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM transactions WHERE id = ?');\n        return stmt.get(id);\n    },\n    findByUserId: (userId, limit = 50)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT ?');\n        return stmt.all(userId, limit);\n    },\n    updateStatus: (id, status)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('UPDATE transactions SET status = ? WHERE id = ?');\n        const result = stmt.run(status, id);\n        return result.changes > 0;\n    }\n};\n/**\n * Phase 2: User Statistics database operations\n */ const userStatsDb = {\n    create: (userId, gameType)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      INSERT INTO user_statistics (user_id, game_type)\n      VALUES (?, ?)\n    `);\n        const result = stmt.run(userId, gameType);\n        return userStatsDb.findByUserAndGame(userId, gameType);\n    },\n    findByUserAndGame: (userId, gameType)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM user_statistics WHERE user_id = ? AND game_type = ?');\n        return stmt.get(userId, gameType);\n    },\n    findByUser: (userId)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM user_statistics WHERE user_id = ?');\n        return stmt.all(userId);\n    },\n    updateStats: (userId, gameType, updates)=>{\n        const db = getDatabase();\n        const existing = userStatsDb.findByUserAndGame(userId, gameType);\n        if (!existing) {\n            userStatsDb.create(userId, gameType);\n        }\n        const fields = Object.keys(updates).map((key)=>`${key} = ?`).join(', ');\n        const values = Object.values(updates);\n        const stmt = db.prepare(`\n      UPDATE user_statistics\n      SET ${fields}, last_played = CURRENT_TIMESTAMP\n      WHERE user_id = ? AND game_type = ?\n    `);\n        const result = stmt.run(...values, userId, gameType);\n        return result.changes > 0;\n    },\n    incrementStats: (userId, gameType, increments)=>{\n        const db = getDatabase();\n        const existing = userStatsDb.findByUserAndGame(userId, gameType);\n        if (!existing) {\n            userStatsDb.create(userId, gameType);\n        }\n        const fields = Object.keys(increments).map((key)=>`${key} = ${key} + ?`).join(', ');\n        const values = Object.values(increments);\n        const stmt = db.prepare(`\n      UPDATE user_statistics\n      SET ${fields}, last_played = CURRENT_TIMESTAMP\n      WHERE user_id = ? AND game_type = ?\n    `);\n        const result = stmt.run(...values, userId, gameType);\n        return result.changes > 0;\n    }\n};\n/**\n * Phase 2: Leaderboards database operations\n */ const leaderboardDb = {\n    updateEntry: (userId, username, gameType, category, value, period)=>{\n        const db = getDatabase();\n        // Calculate period dates\n        const now = new Date();\n        let periodStart, periodEnd;\n        switch(period){\n            case 'daily':\n                periodStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n                periodEnd = new Date(periodStart.getTime() + 24 * 60 * 60 * 1000);\n                break;\n            case 'weekly':\n                const dayOfWeek = now.getDay();\n                periodStart = new Date(now.getTime() - dayOfWeek * 24 * 60 * 60 * 1000);\n                periodStart.setHours(0, 0, 0, 0);\n                periodEnd = new Date(periodStart.getTime() + 7 * 24 * 60 * 60 * 1000);\n                break;\n            case 'monthly':\n                periodStart = new Date(now.getFullYear(), now.getMonth(), 1);\n                periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 1);\n                break;\n            default:\n                periodStart = new Date(2024, 0, 1); // Platform start date\n                periodEnd = new Date(2099, 11, 31); // Far future\n        }\n        const stmt = db.prepare(`\n      INSERT OR REPLACE INTO leaderboards\n      (user_id, username, game_type, category, value, period, period_start, period_end)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(userId, username, gameType, category, value, period, periodStart.toISOString(), periodEnd.toISOString());\n        return result.changes > 0;\n    },\n    getLeaderboard: (gameType, category, period, limit = 10)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      SELECT * FROM leaderboards\n      WHERE game_type = ? AND category = ? AND period = ?\n      ORDER BY value DESC\n      LIMIT ?\n    `);\n        return stmt.all(gameType, category, period, limit);\n    },\n    getUserRank: (userId, gameType, category, period)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      SELECT COUNT(*) + 1 as rank FROM leaderboards\n      WHERE game_type = ? AND category = ? AND period = ? AND value > (\n        SELECT COALESCE(value, 0) FROM leaderboards\n        WHERE user_id = ? AND game_type = ? AND category = ? AND period = ?\n      )\n    `);\n        const result = stmt.get(gameType, category, period, userId, gameType, category, period);\n        return result?.rank || 0;\n    }\n};\n/**\n * Phase 2: Game Sessions database operations\n */ const gameSessionDb = {\n    startSession: (userId)=>{\n        const db = getDatabase();\n        // End any existing active session\n        gameSessionDb.endActiveSession(userId);\n        const stmt = db.prepare(`\n      INSERT INTO game_sessions (user_id)\n      VALUES (?)\n    `);\n        const result = stmt.run(userId);\n        return gameSessionDb.findById(result.lastInsertRowid);\n    },\n    findById: (id)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM game_sessions WHERE id = ?');\n        return stmt.get(id);\n    },\n    findActiveSession: (userId)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM game_sessions WHERE user_id = ? AND is_active = 1 ORDER BY session_start DESC LIMIT 1');\n        return stmt.get(userId);\n    },\n    updateSession: (sessionId, updates)=>{\n        const db = getDatabase();\n        const fields = Object.keys(updates).map((key)=>`${key} = ?`).join(', ');\n        const values = Object.values(updates);\n        const stmt = db.prepare(`UPDATE game_sessions SET ${fields} WHERE id = ?`);\n        const result = stmt.run(...values, sessionId);\n        return result.changes > 0;\n    },\n    endActiveSession: (userId)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      UPDATE game_sessions\n      SET is_active = 0, session_end = CURRENT_TIMESTAMP\n      WHERE user_id = ? AND is_active = 1\n    `);\n        const result = stmt.run(userId);\n        return result.changes > 0;\n    },\n    getUserSessions: (userId, limit = 20)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      SELECT * FROM game_sessions\n      WHERE user_id = ?\n      ORDER BY session_start DESC\n      LIMIT ?\n    `);\n        return stmt.all(userId, limit);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./lib/database.ts\n");

/***/ }),

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fwallet%2Fdeposit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cwallet%5Cdeposit.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fwallet%2Fdeposit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cwallet%5Cdeposit.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_wallet_deposit_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\wallet\\deposit.ts */ \"(api-node)/./pages/api/wallet/deposit.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_api_wallet_deposit_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_pages_api_wallet_deposit_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_wallet_deposit_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_wallet_deposit_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/wallet/deposit\",\n        pathname: \"/api/wallet/deposit\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_wallet_deposit_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fwallet%2Fdeposit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cwallet%5Cdeposit.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/wallet/deposit.ts":
/*!*************************************!*\
  !*** ./pages/api/wallet/deposit.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth */ \"(api-node)/./lib/auth.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(api-node)/./lib/database.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_auth__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_lib_auth__WEBPACK_IMPORTED_MODULE_0__.withAuth)(async (req, res, user)=>{\n    // Initialize database\n    (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.initDatabase)();\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            success: false,\n            error: 'Method not allowed'\n        });\n    }\n    try {\n        const { currency, amount } = req.body;\n        // Validate input\n        if (!currency || ![\n            'USDT',\n            'LTC'\n        ].includes(currency)) {\n            return res.status(400).json({\n                success: false,\n                error: 'Invalid currency. Must be USDT or LTC'\n            });\n        }\n        if (typeof amount !== 'number' || amount <= 0) {\n            return res.status(400).json({\n                success: false,\n                error: 'Invalid amount'\n            });\n        }\n        // Validate amount limits\n        const minDeposit = currency === 'USDT' ? 1 : 0.001;\n        const maxDeposit = currency === 'USDT' ? 10000 : 10;\n        if (amount < minDeposit) {\n            return res.status(400).json({\n                success: false,\n                error: `Minimum deposit is ${minDeposit} ${currency}`\n            });\n        }\n        if (amount > maxDeposit) {\n            return res.status(400).json({\n                success: false,\n                error: `Maximum deposit is ${maxDeposit} ${currency}`\n            });\n        }\n        // Create transaction record\n        const transaction = _lib_database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.create({\n            user_id: user.id,\n            type: 'deposit',\n            currency,\n            amount,\n            status: 'pending'\n        });\n        // In a real application, you would integrate with actual payment processors\n        // For this demo, we'll simulate instant deposits\n        // Generate mock transaction hash\n        const transactionHash = `0x${Math.random().toString(16).substring(2, 66)}`;\n        // Update transaction status and add hash\n        _lib_database__WEBPACK_IMPORTED_MODULE_1__.transactionDb.updateStatus(transaction.id, 'completed');\n        // Add to user balance\n        _lib_database__WEBPACK_IMPORTED_MODULE_1__.userDb.addToBalance(user.id, currency, amount);\n        // Get updated user data\n        const updatedUser = _lib_database__WEBPACK_IMPORTED_MODULE_1__.userDb.findById(user.id);\n        return res.status(200).json({\n            success: true,\n            transaction: {\n                ...transaction,\n                status: 'completed',\n                transaction_hash: transactionHash\n            },\n            balance: {\n                usdt: updatedUser?.usdt_balance || 0,\n                ltc: updatedUser?.ltc_balance || 0\n            },\n            message: `Successfully deposited ${amount} ${currency}`\n        });\n    } catch (error) {\n        console.error('Deposit API error:', error);\n        return res.status(500).json({\n            success: false,\n            error: 'Internal server error'\n        });\n    }\n}));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/wallet/deposit.ts\n");

/***/ }),

/***/ "bcryptjs?cd17":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = import("bcryptjs");;

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("better-sqlite3");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("jsonwebtoken");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fwallet%2Fdeposit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cwallet%5Cdeposit.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();