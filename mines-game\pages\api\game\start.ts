import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { initDatabase, gameDb, userDb } from '@/lib/database';
import { gameFactory } from '@/lib/games/GameFactory';
import { GameType } from '@/types';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  // Reduced logging for performance

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { game_type, bet_amount, betAmount, client_seed, ...gameParams } = req.body;

    // Handle both bet_amount (snake_case) and betAmount (camelCase) for compatibility
    const finalBetAmount = bet_amount || betAmount;

    // Removed excessive logging for performance

    // Validate game type
    if (!game_type || typeof game_type !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Game type is required'
      });
    }

    // Validate bet amount
    if (typeof finalBetAmount !== 'number' || finalBetAmount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid bet amount'
      });
    }

    // Check if user has sufficient balance
    if (finalBetAmount > user.usdt_balance) {
      return res.status(400).json({
        success: false,
        error: 'Insufficient balance'
      });
    }

    // Check if user has an active game
    const activeGame = gameDb.findActiveByUserId(user.id);
    if (activeGame) {
      return res.status(400).json({
        success: false,
        error: 'You already have an active game. Please finish it first.'
      });
    }

    // Ensure game factory is initialized
    await gameFactory.initialize();

    // Check if game type is available
    if (!gameFactory.isGameAvailable(game_type as GameType)) {
      return res.status(400).json({
        success: false,
        error: `Game type '${game_type}' is not available`
      });
    }

    // Create game using factory
    console.log('🚀 API /game/start - Step 7: Creating game with factory...');

    // Prepare game-specific parameters
    const gameCreationParams: any = {
      userId: user.id,
      betAmount: finalBetAmount,
      clientSeed: client_seed
    };

    // Add game-specific parameters
    if (game_type === 'mines') {
      gameCreationParams.mineCount = gameParams.mine_count;
    } else if (game_type === 'dice') {
      gameCreationParams.targetNumber = gameParams.target_number;
      gameCreationParams.rollUnder = gameParams.roll_under;
    } else if (game_type === 'crash') {
      // Convert camelCase to snake_case for crash game
      gameCreationParams.bet_amount = finalBetAmount;
      gameCreationParams.auto_cash_out = gameParams.auto_cash_out;
      gameCreationParams.user_id = user.id;
      gameCreationParams.client_seed = client_seed;
    } else if (game_type === 'limbo') {
      // Handle limbo game parameters
      gameCreationParams.targetMultiplier = gameParams.targetMultiplier;
    }

    const gameResult = await gameFactory.createGame(game_type as GameType, gameCreationParams);

    if (!gameResult.success || !gameResult.game) {
      return res.status(400).json({
        success: false,
        error: gameResult.error || 'Failed to create game'
      });
    }

    // Deduct bet amount from user balance
    const newBalance = user.usdt_balance - finalBetAmount;
    userDb.updateBalance(user.id, 'USDT', newBalance);

    // Save game to database
    const savedGame = gameDb.create(gameResult.game as any);

    // Hide sensitive information for client
    const clientGame = {
      ...savedGame,
      mine_positions: game_type === 'mines' ? [] : savedGame.mine_positions, // Hide mine positions for active games
      server_seed: undefined // Don't send server seed to client
    };

    return res.status(200).json({
      success: true,
      game: clientGame,
      message: 'Game started successfully'
    });

  } catch (error) {
    console.error('🚀 API /game/start - CATCH BLOCK ERROR:', error);
    console.error('🚀 API /game/start - Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});
